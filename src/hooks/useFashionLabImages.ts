import { useState, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { FashionLabImageService } from '../services/fashionLabImageService';
import { toast } from 'react-hot-toast';

interface UseGenerateImagesOptions {
  collectionId: string;
  onSuccess?: (queueId: string) => void;
  onComplete?: (images: string[]) => void;
}

interface GenerateParams {
  prompt: string;
  faceImage: string;
  image2: string;
  image3: string;
  image4: string;
  metadata?: any;
  // Seed values for reproducible generation (optional)
  seed1?: number | null;
  seed2?: number | null;
  seed3?: number | null;
  seed4?: number | null;
  // Number of images to generate (will make multiple API calls)
  numImages?: number;
}

export function useFashionLabImages(options: UseGenerateImagesOptions) {
  const queryClient = useQueryClient();
  const [activeQueueIds, setActiveQueueIds] = useState<string[]>([]);
  const [progress, setProgress] = useState(0);
  const [currentPrompt, setCurrentPrompt] = useState<string>('');

  // Generate images mutation (V2 API)
  const generateMutation = useMutation({
    mutationFn: async (params: GenerateParams) => {
      setCurrentPrompt(params.prompt);

      const numImages = params.numImages || 1;
      const queueIds: string[] = [];

      // Make multiple API calls if numImages > 1
      for (let i = 0; i < numImages; i++) {

        const result = await FashionLabImageService.generateImages({
          prompt: params.prompt,
          faceImage: params.faceImage,
          image2: params.image2,
          image3: params.image3,
          image4: params.image4,
          collectionId: options.collectionId,
          storeOnCompletion: true,
          metadata: params.metadata,
          // Only include seeds if they're provided and this is the first call
          // For multiple images, let each call generate random seeds
          ...(i === 0 && params.seed1 !== undefined && params.seed1 !== null && { seed1: params.seed1 }),
          ...(i === 0 && params.seed2 !== undefined && params.seed2 !== null && { seed2: params.seed2 }),
          ...(i === 0 && params.seed3 !== undefined && params.seed3 !== null && { seed3: params.seed3 }),
          ...(i === 0 && params.seed4 !== undefined && params.seed4 !== null && { seed4: params.seed4 }),
        });

        queueIds.push(result.queue_id);
      }

      return queueIds;
    },
    onSuccess: (queueIds) => {
      setActiveQueueIds(queueIds);
      setProgress(0);
      toast.success(`Image generation started (${queueIds.length} ${queueIds.length === 1 ? 'image' : 'images'})`);

      // For backward compatibility, call onSuccess with the first queue ID
      if (queueIds.length > 0) {
        options.onSuccess?.(queueIds[0]);
      }

      // Start polling for completion of all queue IDs
      queueIds.forEach(queueId => pollForCompletion(queueId));
    },
    onError: (error: Error) => {
      toast.error(`Failed to generate images: ${error.message}`);
    },
  });

  // Poll for completion
  const pollForCompletion = useCallback(async (queueId: string) => {
    try {
      const result = await FashionLabImageService.waitForCompletion(
        queueId,
        options.collectionId,
        {
          onProgress: setProgress,
          prompt: currentPrompt,
        }
      );

      if (result.status === 'completed' && result.stored) {
        toast.success('Images generated and stored successfully');

        // Invalidate queries to refresh image lists
        queryClient.invalidateQueries({ queryKey: ['ai-generated-images', options.collectionId] });
        queryClient.invalidateQueries({ queryKey: ['generated-images', options.collectionId] });

        if (result.images) {
          options.onComplete?.(result.images);
        }
      } else if (result.status === 'failed') {
        toast.error('Image generation failed');
      }
      
      setActiveQueueIds([]);
      setProgress(0);
    } catch (error) {
      toast.error('Failed to complete image generation');
      setActiveQueueIds([]);
      setProgress(0);
    }
  }, [options, queryClient, currentPrompt]);

  // Query for generated images
  const generatedImagesQuery = useQuery({
    queryKey: ['generated-images', options.collectionId],
    queryFn: () => FashionLabImageService.getGeneratedImages(options.collectionId),
    enabled: !!options.collectionId,
  });

  // Check status manually
  const checkStatus = useCallback(async (queueId: string) => {
    const result = await FashionLabImageService.checkQueueStatus(
      queueId,
      options.collectionId
    );
    return result;
  }, [options.collectionId]);

  return {
    generate: generateMutation.mutate,
    isGenerating: generateMutation.isPending || activeQueueIds.length > 0,
    progress,
    activeQueueIds,
    // For backward compatibility, return the first queue ID
    activeQueueId: activeQueueIds.length > 0 ? activeQueueIds[0] : null,
    generatedImages: generatedImagesQuery.data || [],
    isLoadingImages: generatedImagesQuery.isLoading,
    checkStatus,
    refetchImages: generatedImagesQuery.refetch,
  };
}