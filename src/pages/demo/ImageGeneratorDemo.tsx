import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '../../components/ui/button';
import { Card, CardContent } from '../../components/ui/card';
import { Textarea } from '../../components/ui/textarea';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Slider } from '../../components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../../components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/badge';
import { Progress } from '../../components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { useToast } from '../../components/ui/use-toast';
import { Alert, AlertDescription } from '../../components/ui/alert';
import {
  Upload, ArrowLeft,
  Sparkles, ChevronDown,
  Maximize2, Edit, Check, Copy,
  ZoomIn, Save, X, Plus, History, Star, Info, CheckCircle, AlertCircle, Expand
} from 'lucide-react';
import { cn, getAssetUrl } from '../../components/common/utils/utils';

import { useFashionLabImages } from '../../hooks/useFashionLabImages';
import { supabase } from '../../components/common/utils/supabase';
import { getOrCreateDemoCollection } from '../../utils/demoHelpers';
import { openAIService } from '../../services/openaiService';
import { compressImage, needsCompression } from '../../utils/imageOptimization';
import { useProducts } from '../../components/common/hooks/useProducts';
import { useAssets } from '../../components/common/hooks/useAssets';
import { useCollection } from '../../components/common/hooks/useCollections';
import { useOrganization } from '../../components/common/hooks/useOrganizations';
import { useActiveModels } from '../../hooks/useActiveModels';
import { getModelImageAsBase64, useModelImage } from '../../hooks/useModelLibrary';
import { FlexibleInputBox, FlexibleInput } from '../../components/image-generator/FlexibleInputBox';
import { FashionLabImageService } from '../../services/fashionLabImageService';
import { getModelPreviewImage, modelHasFaceImage } from '../../utils/modelImageUtils';
import { ModelImage } from '../../components/ui/ModelImage';
import { FullScreenImageViewer } from '../../components/common/FullScreenImageViewer';
import { useFullScreenImageViewer } from '../../hooks/useFullScreenImageViewer';

// Static fallback models (used if dynamic model library is empty)
const staticCampaignModels = [
  { 
    id: 'S', 
    name: 'Model S - Scandinavian',
    shortName: 'Model S',
    description: 'Blond scandinavian young woman in her twenties, thin and petite, not too tall',
    promptText: 'this scandinavian fashion model with blonde hair, young woman in her twenties, thin and petite build',
    lora: 'bubbleroom_model_s_v1',
    image: '/Final/5713732546603_M_front.jpg',
  },
  { 
    id: 'M', 
    name: 'Model M - European',
    shortName: 'Model M',
    description: 'European looking white woman with mid length dark brown hair and brown eyes, casual and confident',
    promptText: 'this european fashion model with mid-length dark brown hair, brown eyes, casual and confident demeanor',
    lora: 'bubbleroom_model_m_v1',
    image: '/Final/5715674861424_M_front.jpg',
  },
  { 
    id: 'L', 
    name: 'Model L - African American',
    shortName: 'Model L',
    description: 'African American woman in her thirties, wide hips and tall, long black hair, cheerful and casual',
    promptText: 'this african american fashion model, woman in her thirties, tall with wide hips, long black hair, cheerful expression',
    lora: 'bubbleroom_model_l_v1',
    image: '/Final/5715677190187_M_front.jpg',
  },
  { 
    id: 'XL', 
    name: 'Model XL - Latino',
    shortName: 'Model XL',
    description: 'Latino woman in her forties, wide hips and sand watch figure, feminine attitude, long black hair',
    promptText: 'this latino fashion model, woman in her forties, hourglass figure with wide hips, long black hair, feminine and graceful',
    lora: 'bubbleroom_model_xl_v1',
    image: '/Final/5715678691409_M_front.jpg',
  }
];

// Angles with hidden prompt text
const angleBlocks = [
  // Half-body angles
  { id: 1, name: 'Half-body Front', promptText: 'half body front facing shot' },
  { id: 2, name: 'Half-body Back', promptText: 'half body shot from behind' },
  { id: 3, name: 'Half-body 3/4 Left', promptText: 'half body three quarter angle left' },
  { id: 4, name: 'Half-body 3/4 Right', promptText: 'half body three quarter angle right' },
  // Full-height angles
  { id: 5, name: 'Full-height Front', promptText: 'full height front facing shot' },
  { id: 6, name: 'Full-height Back', promptText: 'full height shot from behind' },
  { id: 7, name: 'Full-height Side Left', promptText: 'full height left side profile' },
  { id: 8, name: 'Full-height Side Right', promptText: 'full height right side profile' },
  { id: 9, name: 'Full-height 3/4 Left', promptText: 'full height three quarter angle left' },
  { id: 10, name: 'Full-height 3/4 Right', promptText: 'full height three quarter angle right' }
];

// Angle preview image mapping (for UI display only)
const anglePreviewMapping: Record<string, string> = {
  'Half-body Front': 'images/angles/halfbody_front.jpg',
  'Half-body Back': 'images/angles/halfbody_back.jpg',
  'Half-body 3/4 Left': 'images/angles/halfbody_3:4_left.jpg',
  'Half-body 3/4 Right': 'images/angles/halfbody_3:4_right.jpg',
  'Full-height Front': 'images/angles/fullheight_front.jpg',
  'Full-height Back': 'images/angles/fullheight_back.jpg',
  'Full-height Side Left': 'images/angles/fullheight_side_left.jpg',
  'Full-height Side Right': 'images/angles/fullheight_side_right.jpg',
  'Full-height 3/4 Left': 'images/angles/fullheight_3:4_left.jpg',
  'Full-height 3/4 Right': 'images/angles/fullheight_3:4_right.jpg'
};

// Static model image file mapping (fallback when dynamic library is not available)
const staticModelImageMapping: Record<string, Record<string, string>> = {
  'S': {
    // Face image
    face: 'Library Models/Small/S_face.webp',
    // Half-body angles
    'Half-body Front': 'Library Models/Small/S_half body face on.jpg',
    'Half-body Back': 'Library Models/Small/S_hlafbody_backside.jpg', // Note: typo in filename
    'Half-body 3/4 Left': 'Library Models/Small/S_half body_3_4angle_left.jpg',
    'Half-body 3/4 Right': 'Library Models/Small/S_half body_3_4angle right.jpg',
    // Full-height angles
    'Full-height Front': 'Library Models/Small/S_fullbody_faceonjpg.jpg', // Note: missing dot
    'Full-height Back': 'Library Models/Small/S_fullbody_backside.jpg',
    'Full-height Side Left': 'Library Models/Small/S_fullbody_side_left.jpg',
    'Full-height Side Right': 'Library Models/Small/S_fullbody_side_right.jpg',
    'Full-height 3/4 Left': 'Library Models/Small/S_full body_3_4angle_left.jpg',
    'Full-height 3/4 Right': 'Library Models/Small/S_fullbody_3_4angle-right.jpg'
  },
  // Placeholder mappings for other models - these would need actual file paths
  'M': {
    face: 'Library Models/m_face.jpg', // Placeholder - update with actual paths
    'Half-body Front': 'Library Models/m_half_front.jpg',
    'Half-body Back': 'Library Models/m_half_back.jpg',
    'Half-body 3/4 Left': 'Library Models/m_half_34_left.jpg',
    'Half-body 3/4 Right': 'Library Models/m_half_34_right.jpg',
    'Full-height Front': 'Library Models/m_full_front.jpg',
    'Full-height Back': 'Library Models/m_full_back.jpg',
    'Full-height Side Left': 'Library Models/m_full_side_left.jpg',
    'Full-height Side Right': 'Library Models/m_full_side_right.jpg',
    'Full-height 3/4 Left': 'Library Models/m_full_34_left.jpg',
    'Full-height 3/4 Right': 'Library Models/m_full_34_right.jpg'
  },
  'L': {
    face: 'Library Models/l_face.jpg',
    'Half-body Front': 'Library Models/l_half_front.jpg',
    'Half-body Back': 'Library Models/l_half_back.jpg',
    'Half-body 3/4 Left': 'Library Models/l_half_34_left.jpg',
    'Half-body 3/4 Right': 'Library Models/l_half_34_right.jpg',
    'Full-height Front': 'Library Models/l_full_front.jpg',
    'Full-height Back': 'Library Models/l_full_back.jpg',
    'Full-height Side Left': 'Library Models/l_full_side_left.jpg',
    'Full-height Side Right': 'Library Models/l_full_side_right.jpg',
    'Full-height 3/4 Left': 'Library Models/l_full_34_left.jpg',
    'Full-height 3/4 Right': 'Library Models/l_full_34_right.jpg'
  },
  'XL': {
    face: 'Library Models/xl_face.jpg',
    'Half-body Front': 'Library Models/xl_half_front.jpg',
    'Half-body Back': 'Library Models/xl_half_back.jpg',
    'Half-body 3/4 Left': 'Library Models/xl_half_34_left.jpg',
    'Half-body 3/4 Right': 'Library Models/xl_half_34_right.jpg',
    'Full-height Front': 'Library Models/xl_full_front.jpg',
    'Full-height Back': 'Library Models/xl_full_back.jpg',
    'Full-height Side Left': 'Library Models/xl_full_side_left.jpg',
    'Full-height Side Right': 'Library Models/xl_full_side_right.jpg',
    'Full-height 3/4 Left': 'Library Models/xl_full_34_left.jpg',
    'Full-height 3/4 Right': 'Library Models/xl_full_34_right.jpg'
  }
};

// Settings blocks for background and art direction
const settingBlocks = [
  {
    id: 'background',
    name: 'Background',
    icon: '🏞️',
    options: [
      { id: 1, name: 'White Seamless', promptText: 'on a white studio background' },
      { id: 2, name: 'Urban Street', promptText: 'on this urban street setting with natural daylight' },
      { id: 3, name: 'Nature Park', promptText: 'on this outdoor park setting with natural greenery' }
    ]
  },
  {
    id: 'artDirection',
    name: 'Art Direction',
    icon: '🎨',
    options: [
      { id: 1, name: 'Editorial', promptText: 'shot from low angle' },
      { id: 2, name: 'E-commerce', promptText: 'shot from eye level' },
      { id: 3, name: 'Lifestyle', promptText: 'shot from high angle' }
    ]
  }
];

// This is now replaced by real products from the database

// Version history mock data
const mockVersionHistory = [
  { id: 1, type: 'reference', name: 'Original Reference', timestamp: '2024-01-15 10:00' },
  { id: 2, type: 'raw', name: 'Generated v1', timestamp: '2024-01-15 10:30' },
  { id: 3, type: 'refined', name: 'Refined v1', timestamp: '2024-01-15 11:00' },
  { id: 4, type: 'refined', name: 'Refined v2', timestamp: '2024-01-15 11:30' },
  { id: 5, type: 'upscaled', name: 'Upscaled Final', timestamp: '2024-01-15 12:00' },
];


interface GenerationSettings {
  prompt: string;
  seed: number | null;
  cfg: number;
  fluxGuidance: number;
  numImages: number;
  ratio: string;
  imageFormat: string;
}


export default function ImageGeneratorDemo() {
  const navigate = useNavigate();
  const { collectionId } = useParams<{ collectionId: string }>();

  const { toast } = useToast();
  
  // Use Fashion Lab V2 API hook
  const {
    generate: generateV2,
    isGenerating: isGeneratingV2,
    progress: progressV2,
    generatedImages: v2GeneratedImages,
    refetchImages
  } = useFashionLabImages({
    collectionId: collectionId!,
    onSuccess: (queueId) => {
      console.log('V2 Generation started:', queueId);
    },
    onComplete: (images) => {
      console.log('V2 Generation complete:', images);
      refetchImages();
    }
  });
  
  // Fetch collection data
  const { data: collection, isLoading: collectionLoading } = useCollection(collectionId);
  
  // Fetch organization data if collection exists
  const { data: organization } = useOrganization(collection?.organization_id);
  
  // Fetch dynamic model library data
  const { 
    modelBlocks: dynamicModelBlocks, 
    modelImageMapping: dynamicModelImageMapping,
    isLoading: modelsLoading 
  } = useActiveModels();
  
  // Use dynamic models if available, otherwise fall back to static
  const campaignModels = dynamicModelBlocks.length > 0 ? dynamicModelBlocks.map((model, index) => ({
    id: model.id, // This is the code (S, M, L, XL)
    name: model.name,
    shortName: model.name,
    description: '',
    promptText: `this fashion model`, // Generic prompt, can be enhanced later
    lora: `bubbleroom_model_${model.id.toLowerCase()}_v1`,
    image: getModelPreviewImage(dynamicModelImageMapping[model.id], model.id)
  })) : staticCampaignModels;
  
  // Use dynamic model image mapping if available
  const modelImageMapping = Object.keys(dynamicModelImageMapping).length > 0 ? dynamicModelImageMapping : staticModelImageMapping;
  
  // Fetch real products from the collection
  const { data: products = [], isLoading: productsLoading } = useProducts({
    collectionId,
    sortBy: 'name',
    sortOrder: 'asc'
  });
  
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [showImageSelector, setShowImageSelector] = useState(false);
  const [pendingProductSelection, setPendingProductSelection] = useState<any>(null);
  
  // Initialize with first product when products are loaded
  useEffect(() => {
    if (products.length > 0 && !selectedProduct) {
      // Don't auto-select, let user choose
      // setSelectedProduct(products[0]);
      // setSelectedProductId(products[0].id);
    }
  }, [products, selectedProduct]);
  
  // Fetch assets for the selected product
  const { data: productAssets = [] } = useAssets({
    collectionId,
    productId: selectedProductId || undefined,
    enabled: !!selectedProductId
  });
  
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [selectedAngles, setSelectedAngles] = useState<number[]>([]);
  const [selectedGarments, setSelectedGarments] = useState<string[]>([]);
  const [selectedSettings, setSelectedSettings] = useState<{[key: string]: number}>({});
  const [artDirection, setArtDirection] = useState('');
  
  // Technical settings
  const [settings, setSettings] = useState<GenerationSettings>({
    prompt: '',
    seed: null,
    cfg: 7.5,
    fluxGuidance: 0.7,
    numImages: 4,
    ratio: '9:16',
    imageFormat: 'jpeg',
  });
  
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isMovingImages, setIsMovingImages] = useState(false);
  
  // New states for prompt building
  const [showPromptHistory, setShowPromptHistory] = useState(false);
  const [promptHistory, setPromptHistory] = useState<string[]>([]);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [activeBlocks, setActiveBlocks] = useState<{type: string, name: string, text: string}[]>([]);
  const [isEditingPrompt, setIsEditingPrompt] = useState(false);
  const [manuallyEdited, setManuallyEdited] = useState(false);
  
  // Uploaded garments state
  const [uploadedGarments, setUploadedGarments] = useState<Array<{
    id: string;
    name: string;
    promptText: string;
    image: string;
    uploading?: boolean;
    file?: File;
  }>>([]);

  // V2 API image states
  const [v2Images, setV2Images] = useState<{
    face?: { file: File; preview: string; base64?: string };
    image_2?: { file: File; preview: string; base64?: string };
    image_3?: { file: File; preview: string; base64?: string };
    image_4?: { file: File; preview: string; base64?: string };
  }>({});
  
  // Flexible inputs for extra elements
  const [flexibleInputs, setFlexibleInputs] = useState<FlexibleInput[]>([]);

  // Store custom angle prompts from database
  const [customAnglePrompts, setCustomAnglePrompts] = useState<Record<string, string>>({});

  // Full-screen image viewer state - only V2 images
  const allGeneratedImages = v2GeneratedImages.map((img: any) => ({
    id: img.id,
    url: `${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/ai-generated/${img.storage_path}`,
    title: 'Generated Image',
    description: `Generated with Fashion Lab V2 API`
  }));

  const {
    isOpen: isFullScreenOpen,
    currentIndex: fullScreenIndex,
    openViewer: openFullScreenViewer,
    closeViewer: closeFullScreenViewer,
    navigateToImage: navigateFullScreenImage
  } = useFullScreenImageViewer(allGeneratedImages);

  const totalImagesToGenerate = selectedModels.length * selectedAngles.length;

  // Initialize prompt with the new style
  useEffect(() => {
    if (!manuallyEdited) {
      buildNewStylePrompt();
    }
  }, [selectedModels, selectedAngles, selectedGarments, selectedSettings, v2Images, flexibleInputs, customAnglePrompts, manuallyEdited]);

  // Check if a model has the required images
  const checkModelImages = (modelId: string): { hasFace: boolean; hasAngle: boolean; angleName?: string } => {
    const hasFace = modelHasFaceImage(dynamicModelImageMapping[modelId]);
    let hasAngle = true;
    let angleName = '';

    if (selectedAngles.length > 0) {
      const angle = angleBlocks.find(a => a.id === selectedAngles[0]);
      if (angle) {
        angleName = angle.name;
        hasAngle = !!dynamicModelImageMapping[modelId]?.[angle.name];
      }
    }

    return { hasFace, hasAngle, angleName };
  };

  // Validate generation requirements
  const validateGenerationRequirements = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    // Check if model is selected
    if (selectedModels.length === 0) {
      errors.push('Please select a model');
    } else {
      // Check if model has required images
      const modelCheck = checkModelImages(selectedModels[0]);
      if (!modelCheck.hasFace) {
        const model = campaignModels.find(m => m.id === selectedModels[0]);
        errors.push(`${model?.name || 'Selected model'} needs a face photo. Please upload it in the Model Library.`);
      }
      
      if (selectedAngles.length > 0 && !modelCheck.hasAngle) {
        const model = campaignModels.find(m => m.id === selectedModels[0]);
        errors.push(`${model?.name || 'Selected model'} needs a photo for the ${modelCheck.angleName} angle.`);
      }
    }
    
    // Check if angle is selected
    if (selectedAngles.length === 0) {
      errors.push('Please select an angle');
    }
    
    // Check if product is selected
    if (!selectedProduct) {
      errors.push('Please select a product');
    }
    
    return { isValid: errors.length === 0, errors };
  };

  // Build prompt in the new FLUX.1 Kontext style
  const buildNewStylePrompt = () => {
    const promptParts = [];
    
    // Start with "Place" or "Create"
    promptParts.push('Place');
    
    // 1. Model description (face and body)
    if (selectedModels.length > 0) {
      const model = campaignModels.find(m => m.id === selectedModels[0]);
      if (model) {
        // Check if we have a custom face prompt from the database
        const customFacePromptKey = `${selectedModels[0]}_face`;
        const customFacePrompt = customAnglePrompts[customFacePromptKey];
        
        // Use custom face prompt if available, otherwise use default model prompt
        promptParts.push(customFacePrompt || model.promptText);
      }
    }
    
    // 2. Main garment from product selection
    const productGarment = uploadedGarments.find(g => g.id === `product-${selectedProduct?.id}`);
    if (productGarment && !productGarment.uploading) {
      promptParts.push(`wearing this main garment: ${productGarment.promptText} as the featured product`);
    }
    
    // 3. Additional garments - make clear these are accessories/complementary items
    const additionalGarments = selectedGarments
      .filter(id => !id.startsWith('product-'))
      .map(id => uploadedGarments.find(g => g.id === id))
      .filter(g => g && !g.uploading);
    
    if (additionalGarments.length > 0) {
      const additionalDescriptions = additionalGarments.map((garment, index) => {
        if (index === 0) {
          return `styled with ${garment.promptText}`;
        } else {
          return `and ${garment.promptText}`;
        }
      });
      promptParts.push(...additionalDescriptions);
    }
    
    // 4. Setting/Background
    if (selectedSettings.background) {
      const bgOption = settingBlocks.find(s => s.id === 'background')?.options.find(o => o.id === selectedSettings.background);
      if (bgOption) {
        promptParts.push(bgOption.promptText);
      }
    }
    
    // 5. Angle
    if (selectedAngles.length > 0) {
      const angle = angleBlocks.find(a => a.id === selectedAngles[0]);
      if (angle) {
        // Check if we have a custom angle prompt from the database
        const customPromptKey = `${selectedModels[0]}_${angle.name}`;
        const customPrompt = customAnglePrompts[customPromptKey];
        
        // Use custom prompt if available, otherwise use default
        promptParts.push(customPrompt || angle.promptText);
      }
    }
    
    // 6. Art direction (camera angle)
    if (selectedSettings.artDirection) {
      const artOption = settingBlocks.find(s => s.id === 'artDirection')?.options.find(o => o.id === selectedSettings.artDirection);
      if (artOption) {
        promptParts.push(artOption.promptText);
      }
    }
    
    // 7. Flexible inputs
    const textInputs = flexibleInputs.filter(input => input.type === 'text' && input.value);
    if (textInputs.length > 0) {
      textInputs.forEach(input => {
        promptParts.push(`with ${input.label.toLowerCase()}: ${input.value}`);
      });
    }
    
    const colorInputs = flexibleInputs.filter(input => input.type === 'color' && input.value);
    if (colorInputs.length > 0) {
      colorInputs.forEach(input => {
        promptParts.push(`using ${input.label.toLowerCase()} color ${input.value}`);
      });
    }
    
    const newPrompt = promptParts.filter(p => p).join(', ');
    setSettings(prev => ({ ...prev, prompt: newPrompt || 'Place' }));
  };
  
  // Function to handle adding text to prompt (for manual editing)
  const addToPrompt = (text: string) => {
    // This is now handled by buildNewStylePrompt
  };

  // Function to load custom angle prompt from database
  const loadCustomAnglePrompt = async (modelCode: string, angleName: string): Promise<string | null> => {
    try {
      if (Object.keys(dynamicModelImageMapping).length > 0) {
        const angleTypeMap: Record<string, string> = {
          'Half-body Front': 'half-body-front',
          'Half-body Back': 'half-body-back',
          'Half-body 3/4 Left': 'half-body-34-left',
          'Half-body 3/4 Right': 'half-body-34-right',
          'Full-height Front': 'full-body-front',
          'Full-height Back': 'full-body-back',
          'Full-height Side Left': 'full-body-side-left',
          'Full-height Side Right': 'full-body-side-right',
          'Full-height 3/4 Left': 'full-body-34-left',
          'Full-height 3/4 Right': 'full-body-34-right'
        };
        
        const dbAngleType = angleTypeMap[angleName] || angleName;
        
        // First get the model UUID from the code
        const { data: modelData } = await supabase
          .from('model_library')
          .select('id')
          .eq('code', modelCode)
          .eq('is_active', true)
          .maybeSingle();
          
        if (!modelData) return null;
        
        // Query the database for this specific model and angle
        const { data } = await supabase
          .from('model_images')
          .select('angle_prompt_text')
          .eq('model_id', modelData.id)
          .eq('angle_type', dbAngleType)
          .maybeSingle();
        
        if (data?.angle_prompt_text) {
          const key = `${modelCode}_${angleName}`;
          setCustomAnglePrompts(prev => ({
            ...prev,
            [key]: data.angle_prompt_text
          }));
          return data.angle_prompt_text;
        }
      }
    } catch (error) {
      console.error('Error loading custom angle prompt:', error);
    }
    return null;
  };

  // Function to load model images from Library Models folder or Supabase
  const loadModelImage = async (modelId: string, imageType: 'face' | string) => {
    try {
      // First try to get image from model library using the hook function
      if (Object.keys(dynamicModelImageMapping).length > 0) {
        // We have dynamic model data, try to use the database approach
        const angleTypeMap: Record<string, string> = {
          'face': 'face',
          'Half-body Front': 'half-body-front',
          'Half-body Back': 'half-body-back',
          'Half-body 3/4 Left': 'half-body-34-left',
          'Half-body 3/4 Right': 'half-body-34-right',
          'Full-height Front': 'full-body-front',
          'Full-height Back': 'full-body-back',
          'Full-height Side Left': 'full-body-side-left',
          'Full-height Side Right': 'full-body-side-right',
          'Full-height 3/4 Left': 'full-body-34-left',
          'Full-height 3/4 Right': 'full-body-34-right'
        };
        
        const dbAngleType = angleTypeMap[imageType] || imageType;
        const base64 = await getModelImageAsBase64(modelId, dbAngleType);
        
        if (base64) {
          // Create a blob from base64
          const base64Data = base64.split(',')[1];
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: 'image/webp' });
          const file = new File([blob], `${modelId}_${dbAngleType}.webp`, { type: 'image/webp' });
          const preview = URL.createObjectURL(blob);
          
          if (imageType === 'face') {
            setV2Images(prev => ({
              ...prev,
              face: { file, preview, base64 }
            }));
          } else {
            setV2Images(prev => ({
              ...prev,
              image_2: { file, preview, base64 }
            }));
          }
          return;
        }
      }
      
      // Fallback to static mapping
      const imagePath = modelImageMapping[modelId]?.[imageType];
      if (!imagePath) {
        console.warn(`No image mapping found for model ${modelId} and type ${imageType}`);
        return;
      }

      let response: Response;
      
      // Check if it's a full URL (from Supabase) or a relative path
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        // It's from Supabase storage
        response = await fetch(imagePath);
      } else {
        // It's a local path in public directory
        response = await fetch(`/${imagePath}`);
      }
      
      if (!response.ok) {
        throw new Error(`Failed to load image: ${response.statusText}`);
      }
      
      const blob = await response.blob();
      const filename = imagePath.split('/').pop() || 'model.jpg';
      const file = new File([blob], filename, { type: blob.type });
      
      // Convert to base64
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        const preview = URL.createObjectURL(blob);
        
        if (imageType === 'face') {
          // Load face image to slot 1
          setV2Images(prev => ({
            ...prev,
            face: { file, preview, base64 }
          }));
        } else {
          // Load angle image to slot 2
          setV2Images(prev => ({
            ...prev,
            image_2: { file, preview, base64 }
          }));
        }
      };
      reader.readAsDataURL(blob);
    } catch (error) {
      console.error(`Error loading model image: ${error}`);
    }
  };

  // Toggle functions for blocks
  const toggleModel = (modelId: string) => {
    const model = campaignModels.find(m => m.id === modelId);
    if (!model) return;
    
    if (selectedModels.includes(modelId)) {
      // Deselecting current model
      setSelectedModels([]);
      // Remove from active blocks
      setActiveBlocks(prev => prev.filter(block => block.type !== 'model'));
      // Clear V2 face image
      setV2Images(prev => ({ ...prev, face: undefined }));
    } else {
      // Selecting new model - only allow one
      setSelectedModels([modelId]);
      
      // Check if model has required images
      const modelCheck = checkModelImages(modelId);
      if (!modelCheck.hasFace) {
        toast({
          title: "Missing Face Photo",
          description: `${model.name} needs a face photo. Please upload it in the Model Library.`,
          variant: "default",
        });
      }
      
      // Load face image for the selected model
      loadModelImage(modelId, 'face');
      
      // Load custom face prompt if available and update active blocks
      loadCustomAnglePrompt(modelId, 'face').then((customPrompt) => {
        // Remove any existing model from active blocks and add new one with custom prompt
        setActiveBlocks(prev => [
          ...prev.filter(block => block.type !== 'model'),
          { type: 'model', name: model.shortName, text: customPrompt || model.promptText }
        ]);
      });
      
      // If an angle is already selected, load the angle image too
      if (selectedAngles.length > 0) {
        const angle = angleBlocks.find(a => a.id === selectedAngles[0]);
        if (angle) {
          loadModelImage(modelId, angle.name);
          // Load custom angle prompt if available
          loadCustomAnglePrompt(modelId, angle.name);
        }
      }
    }
  };

  const toggleAngle = (angleId: number) => {
    const angle = angleBlocks.find(a => a.id === angleId);
    if (!angle) return;
    
    if (selectedAngles.includes(angleId)) {
      // Deselecting current angle
      setSelectedAngles([]);
      setActiveBlocks(prev => prev.filter(block => block.type !== 'angle'));
      // Clear V2 angle image
      setV2Images(prev => ({ ...prev, image_2: undefined }));
    } else {
      // Selecting new angle - only allow one
      setSelectedAngles([angleId]);
      
      // Load angle image for the selected model
      if (selectedModels.length > 0) {
        const modelId = selectedModels[0];
        const model = campaignModels.find(m => m.id === modelId);
        
        // Check if this angle has an image for the selected model
        if (!dynamicModelImageMapping[modelId]?.[angle.name]) {
          toast({
            title: "Missing Angle Photo",
            description: `${model?.name || 'Selected model'} needs a photo for the ${angle.name} angle. Please upload it in the Model Library.`,
            variant: "default",
          });
        }
        
        loadModelImage(modelId, angle.name);
        
        // Load custom angle prompt if available and update active blocks
        loadCustomAnglePrompt(modelId, angle.name).then((customPrompt) => {
          // Remove any existing angle from active blocks and add new one
          setActiveBlocks(prev => [
            ...prev.filter(block => block.type !== 'angle'),
            { type: 'angle', name: angle.name, text: customPrompt || angle.promptText }
          ]);
        });
      } else {
        // No model selected, just use default angle prompt
        setActiveBlocks(prev => [
          ...prev.filter(block => block.type !== 'angle'),
          { type: 'angle', name: angle.name, text: angle.promptText }
        ]);
      }
    }
  };

  const toggleGarment = (garmentId: string) => {
    const garment = uploadedGarments.find(g => g.id === garmentId);
    if (!garment) return;
    
    if (selectedGarments.includes(garmentId)) {
      // Deselecting garment
      setSelectedGarments(selectedGarments.filter(id => id !== garmentId));
      setActiveBlocks(prev => prev.filter(block => !(block.type === 'garment' && block.text === garment.promptText)));
      
      // Remove from V2 images if it's there
      const v2Slots: Array<'image_2' | 'image_3' | 'image_4'> = ['image_2', 'image_3', 'image_4'];
      v2Slots.forEach(slot => {
        if (v2Images[slot]?.file === garment.file) {
          removeV2Image(slot);
        }
      });
    } else {
      // Selecting garment
      setSelectedGarments([...selectedGarments, garmentId]);
      setActiveBlocks(prev => [...prev, { type: 'garment', name: garment.name, text: garment.promptText }]);
      
      // Auto-add to slot 4 if file exists (slot 2 is for angle, slot 3 is for product)
      if (garment.file && !v2Images.image_4) {
        // Convert file to base64 and add to V2 images
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64 = reader.result as string;
          setV2Images(prev => ({
            ...prev,
            image_4: { file: garment.file, preview: garment.image, base64 }
          }));
        };
        reader.readAsDataURL(garment.file);
      }
    }
  };

  const toggleSetting = (categoryId: string, optionId: number) => {
    const category = settingBlocks.find(s => s.id === categoryId);
    const option = category?.options.find(o => o.id === optionId);
    if (!option) return;
    
    // Remove previous selection from this category
    if (selectedSettings[categoryId]) {
      const prevOption = category?.options.find(o => o.id === selectedSettings[categoryId]);
      if (prevOption) {
        setActiveBlocks(prev => prev.filter(block => !(block.type === categoryId && block.text === prevOption.promptText)));
      }
    }
    
    // Add new selection
    setSelectedSettings({ ...selectedSettings, [categoryId]: optionId });
    setActiveBlocks(prev => [...prev, { type: categoryId, name: option.name, text: option.promptText }]);
  };

  const toggleImageSelection = (imageId: string) => {
    if (selectedImages.includes(imageId)) {
      setSelectedImages(selectedImages.filter((id) => id !== imageId));
    } else {
      setSelectedImages([...selectedImages, imageId]);
    }
  };

  const selectAllImages = () => {
    setSelectedImages(v2GeneratedImages.map((img: any) => img.id));
  };

  const moveSelectedToCollection = async (imageIds?: string[]) => {
    const idsToMove = imageIds || selectedImages;
    if (!collectionId || idsToMove.length === 0) return;
    
    setIsMovingImages(true);
    try {
      // Get selected images data
      const imagesToMove = v2GeneratedImages.filter((img: any) =>
        idsToMove.includes(img.id)
      );

      // Use the new service method that processes images through compression pipeline
      await FashionLabImageService.selectGeneratedImages(
        imagesToMove.map((img: any) => img.id),
        collectionId
      );
      
      toast({
        title: 'Success',
        description: `Moved ${idsToMove.length} ${idsToMove.length === 1 ? 'image' : 'images'} to collection`,
      });
      
      // Clear selection
      setSelectedImages([]);
      
      // Refresh generated images
      refetchImages();
      
    } catch (error) {
      console.error('Error moving images:', error);
      toast({
        title: 'Error',
        description: 'Failed to move images to collection',
        variant: 'destructive',
      });
    } finally {
      setIsMovingImages(false);
    }
  };
  
  // Remove a specific block from prompt
  const removeBlockFromPrompt = (blockText: string, blockType: string) => {
    setActiveBlocks(prev => prev.filter(block => block.text !== blockText));
    
    // Update the corresponding selection state
    if (blockType === 'model') {
      const model = campaignModels.find(m => m.promptText === blockText);
      if (model) setSelectedModels(prev => prev.filter(id => id !== model.id));
    } else if (blockType === 'angle') {
      const angle = angleBlocks.find(a => a.promptText === blockText);
      if (angle) setSelectedAngles(prev => prev.filter(id => id !== angle.id));
    } else if (blockType === 'garment') {
      const garment = uploadedGarments.find(g => g.promptText === blockText);
      if (garment) setSelectedGarments(prev => prev.filter(id => id !== garment.id));
    }
  };

  // Color system for different block types
  const BLOCK_COLORS = {
    model: { bg: 'bg-blue-100', text: 'text-blue-900', border: 'border-blue-200' },
    angle: { bg: 'bg-green-100', text: 'text-green-900', border: 'border-green-200' },
    product: { bg: 'bg-indigo-100', text: 'text-indigo-900', border: 'border-indigo-200' },
    garment: { bg: 'bg-purple-100', text: 'text-purple-900', border: 'border-purple-200' },
    background: { bg: 'bg-orange-100', text: 'text-orange-900', border: 'border-orange-200' },
    artDirection: { bg: 'bg-pink-100', text: 'text-pink-900', border: 'border-pink-200' },
  };

  // Parse prompt and identify block segments
  const parsePromptSegments = (prompt: string) => {
    const segments: Array<{ text: string; type: string | null; blockData?: any }> = [];
    let remainingPrompt = prompt;
    const lastIndex = 0;
    
    // Sort blocks by position in prompt
    const blocksWithPositions = activeBlocks
      .map(block => ({
        ...block,
        index: prompt.indexOf(block.text)
      }))
      .filter(block => block.index !== -1)
      .sort((a, b) => a.index - b.index);
    
    blocksWithPositions.forEach(block => {
      const index = remainingPrompt.indexOf(block.text);
      if (index !== -1) {
        // Add text before block
        if (index > 0) {
          segments.push({ text: remainingPrompt.substring(0, index), type: null });
        }
        // Add block
        segments.push({ 
          text: block.text, 
          type: block.type,
          blockData: block
        });
        remainingPrompt = remainingPrompt.substring(index + block.text.length);
      }
    });
    
    // Add remaining text
    if (remainingPrompt) {
      segments.push({ text: remainingPrompt, type: null });
    }
    
    return segments;
  };
  
  // Apply a prompt template
  const applyTemplate = (template: string) => {
    setSettings(prev => ({
      ...prev,
      prompt: prev.prompt ? `${prev.prompt}, ${template}` : template
    }));
  };
  
  // Update technical settings in prompt
  const updateTechnicalSettingInPrompt = (settingType: string, value: string | number) => {
    setSettings(prev => {
      let newPrompt = prev.prompt;
      
      // Remove existing setting if present
      const regex = new RegExp(`\\b${settingType}:[^,\\s]+`, 'g');
      newPrompt = newPrompt.replace(regex, '').trim();
      
      // Clean up any double commas or spaces
      newPrompt = newPrompt.replace(/,\s*,/g, ',').replace(/\s+/g, ' ').trim();
      
      // Add new setting at the end
      if (value && value !== 0) {
        newPrompt = newPrompt ? `${newPrompt}, ${settingType}:${value}` : `${settingType}:${value}`;
      }
      
      return { ...prev, prompt: newPrompt };
    });
  };
  
  // Save current prompt to history
  const saveToHistory = () => {
    if (settings.prompt && !promptHistory.includes(settings.prompt)) {
      setPromptHistory(prev => [settings.prompt, ...prev].slice(0, 10)); // Keep last 10
    }
  };

  // Handle garment upload (supports multiple files)
  const handleGarmentUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Process each file
    Array.from(files).forEach(async (file, index) => {
      // Add a small delay between processing files to avoid overwhelming the API
      setTimeout(async () => {
        // Create a temporary ID
        const tempId = `temp-${Date.now()}-${index}`;
        
        // Create object URL for preview
        const imageUrl = URL.createObjectURL(file);
        
        // Add temporary garment with loading state
        const tempGarment = {
          id: tempId,
          name: file.name.split('.')[0],
          promptText: 'Analyzing garment...',
          image: imageUrl,
          uploading: true,
          file: file // Store the file for later use
        };
        
        setUploadedGarments(prev => [...prev, tempGarment]);
        
        // Compress image if needed before processing
        let processedBase64: string;
        
        try {
          if (needsCompression(file, 2)) { // Compress if larger than 2MB
            console.log(`Compressing ${file.name} before processing...`);
            processedBase64 = await compressImage(file, 1024, 1024, 0.8);
          } else {
            // Convert to base64 without compression
            processedBase64 = await new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = (e) => resolve(e.target?.result as string);
              reader.onerror = reject;
              reader.readAsDataURL(file);
            });
          }
          
          // Use OpenAI to analyze the garment
          const analysis = await openAIService.analyzeGarmentImage(processedBase64);
          
          // Update garment with AI description
          setUploadedGarments(prev => 
            prev.map(g => 
              g.id === tempId 
                ? { ...g, promptText: analysis.description, uploading: false }
                : g
            )
          );
          
          // Auto-add to V2 slot 4 (extra garment slot)
          setV2Images(prev => {
            const availableSlot = !prev.image_4 ? 'image_4' : null;
            
            if (availableSlot) {
              // Automatically select this garment
              setSelectedGarments(prevSelected => [...prevSelected, tempId]);
              
              // Add to active blocks for prompt building
              setActiveBlocks(prevBlocks => [...prevBlocks, { 
                type: 'garment', 
                name: tempGarment.name, 
                text: analysis.description 
              }]);
              
              return {
                ...prev,
                [availableSlot]: { file, preview: imageUrl, base64: processedBase64 } // Use compressed base64
              };
            }
            return prev;
          });
        } catch (error) {
          console.error('Error processing garment:', error);
          // Fallback to mock description
          const mockDescriptions = [
            'sleek black leather jacket with silver zippers, fitted silhouette',
            'flowing silk scarf with abstract pattern, vibrant colors',
            'vintage denim vest with brass buttons, distressed finish',
            'chunky knit sweater in cream color, oversized fit',
            'elegant silk blouse with pearl buttons, flowing sleeves'
          ];
          const fallbackDescription = mockDescriptions[Math.floor(Math.random() * mockDescriptions.length)];
          
          setUploadedGarments(prev => 
            prev.map(g => 
              g.id === tempId 
                ? { ...g, promptText: fallbackDescription, uploading: false }
                : g
            )
          );
        }
      }, index * 500); // 500ms delay between each file
    });

    // Clear the input so the same files can be selected again if needed
    event.target.value = '';
  };

  // Handle V2 API image upload
  const handleV2ImageUpload = async (type: 'face' | 'image_2' | 'image_3' | 'image_4', file: File) => {
    // Create preview URL
    const preview = URL.createObjectURL(file);
    
    // Convert to base64
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64 = reader.result as string;
      setV2Images(prev => ({
        ...prev,
        [type]: { file, preview, base64 }
      }));
    };
    reader.readAsDataURL(file);
  };

  // Remove V2 image
  const removeV2Image = (type: 'face' | 'image_2' | 'image_3' | 'image_4') => {
    if (v2Images[type]?.preview) {
      URL.revokeObjectURL(v2Images[type]!.preview);
    }
    setV2Images(prev => {
      const newImages = { ...prev };
      delete newImages[type];
      return newImages;
    });
    
    // If removing the product image (image_2), also remove from activeBlocks and selectedGarments
    if (type === 'image_2' && selectedProduct) {
      const garmentId = `product-${selectedProduct.id}`;
      setSelectedGarments(prev => prev.filter(id => id !== garmentId));
      setUploadedGarments(prev => prev.filter(g => g.id !== garmentId));
      setActiveBlocks(prev => prev.filter(block => block.type !== 'product'));
    }
  };

  // Handle product selection and image choice
  const handleProductImageSelection = async (asset: any) => {
    if (!selectedProduct) return;
    
    // Convert asset to file for V2 API
    const assetUrl = getAssetUrl(asset, false); // Use compressed version
    
    try {
      const res = await fetch(assetUrl);
      const blob = await res.blob();
      const file = new File([blob], asset.file_name, { type: blob.type });
      
      // Upload to V2 API slot 3 (slot 2 is now for angle images)
      handleV2ImageUpload('image_3', file);
      
      // Check if asset has AI analysis
      let promptText = selectedProduct.description || `${selectedProduct.name} garment`;
      
      // If the asset has metadata with AI description, use it
      if (asset.metadata?.ai_description) {
        promptText = asset.metadata.ai_description;
      } else if (asset.metadata?.description) {
        promptText = asset.metadata.description;
      }
      
      // Add to selected garments for prompt building
      const garmentId = `product-${selectedProduct.id}`;
      setUploadedGarments(prev => {
        const filtered = prev.filter(g => g.id !== garmentId);
        return [...filtered, {
          id: garmentId,
          name: selectedProduct.name,
          promptText: promptText,
          image: assetUrl,
          uploading: false,
          file: file,
          assetId: asset.id
        }];
      });
      
      // Ensure it's selected
      setSelectedGarments(prev => {
        if (!prev.includes(garmentId)) {
          return [...prev, garmentId];
        }
        return prev;
      });
      
      // Add to active blocks
      setActiveBlocks(prev => {
        // Remove any existing product blocks
        const filtered = prev.filter(block => block.type !== 'product');
        // Add the new product block
        return [...filtered, { 
          type: 'product', 
          name: selectedProduct.name, 
          text: `wearing this main garment: ${promptText} as the featured product`
        }];
      });
      
      // Close the image selector
      setShowImageSelector(false);
    } catch (err) {
      console.error('Error loading product image:', err);
    }
  };

  // Auto-populate or show selector when product has assets
  useEffect(() => {
    if (productAssets.length > 0 && selectedProduct && !v2Images.image_2) {
      if (productAssets.length === 1) {
        // Only one image, use it automatically
        handleProductImageSelection(productAssets[0]);
      } else {
        // Multiple images, show selector
        setShowImageSelector(true);
      }
    }
  }, [productAssets, selectedProduct]);

  const generateBatch = async () => {
    // Validate requirements before generating
    const validation = validateGenerationRequirements();
    if (!validation.isValid) {
      toast({
        title: "Cannot Generate Images",
        description: validation.errors.join('. '),
        variant: "destructive",
      });
      return;
    }
    
    // Save prompt to history before generating
    saveToHistory();
    
    // Use collection ID from URL if available, otherwise try to create demo collection
    let targetCollectionId = collectionId;
    if (!targetCollectionId) {
      targetCollectionId = await getOrCreateDemoCollection();
      if (!targetCollectionId) {
        toast({
          title: "Generation Error",
          description: "Failed to create or find a collection for your images.",
          variant: "destructive",
        });
        return;
      }
    }
    
    // Check if we should use V2 API
    // If slot 4 is empty, check if we have a flexible image input to use
    const finalV2Images = { ...v2Images };
    if (!finalV2Images.image_4) {
      const flexibleImage = flexibleInputs.find(input => input.type === 'image' && input.base64);
      if (flexibleImage) {
        finalV2Images.image_4 = {
          file: flexibleImage.file!,
          preview: flexibleImage.preview!,
          base64: flexibleImage.base64!
        };
      }
    }
    
    // Check if all required V2 images are uploaded
    const hasAllRequiredImages = finalV2Images.face?.base64 && finalV2Images.image_2?.base64 &&
                                 finalV2Images.image_3?.base64 && finalV2Images.image_4?.base64;

    if (!hasAllRequiredImages) {
      toast({
        title: "Missing Images",
        description: "Please upload all required images: Face, Model Body Shot, Product Image, and Additional Item.",
        variant: "destructive",
      });
      return;
    }

    // Use V2 API with Fashion Lab images
    try {
      await generateV2({
        prompt: settings.prompt,
        faceImage: finalV2Images.face!.base64!,
        image2: finalV2Images.image_2!.base64!,
        image3: finalV2Images.image_3!.base64!,
        image4: finalV2Images.image_4!.base64!,
        // Include seed values from technical settings
        seed1: settings.seed,
        seed2: settings.seed,
        seed3: settings.seed,
        seed4: settings.seed,
        numImages: settings.numImages,
        metadata: {
          source: 'image-generator-demo',
          selectedModels,
          selectedAngles,
          flexibleInputs: flexibleInputs.length > 0 ? flexibleInputs : undefined
        }
      });
      toast({
        title: "Generation Started",
        description: "Your images are being generated. This may take a few minutes.",
      });
    } catch (error) {
      console.error('V2 Generation error:', error);
      toast({
        title: "Generation Failed",
        description: "An error occurred while generating images. Please try again.",
        variant: "destructive",
      });
    }
  };



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b sticky top-0 z-40">
        <div className="px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (organization?.id && collectionId) {
                    navigate(`/organizations/${organization.id}/collections/${collectionId}`);
                  } else {
                    navigate(-1);
                  }
                }}
                className="gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Collection
              </Button>
              <div className="h-6 w-px bg-border" />
              <h1 className="text-xl font-semibold">FashionLab Image Generator</h1>
            </div>
          </div>
        </div>
        
        {/* Campaign Context Bar */}
        <div className="bg-gray-100 border-b px-6 py-3">
          <div className="flex items-center justify-between text-sm">
            <div>
              <span className="font-medium">Campaign:</span> {organization?.name || 'Loading...'} {collection?.name || ''}
              <span className="mx-4">•</span>
              <span className="font-medium">Products:</span> {selectedProduct ? `${products.findIndex(p => p.id === selectedProduct.id) + 1}/${products.length}` : `0/${products.length}`}
              <span className="mx-4">•</span>
              <span className="font-medium">Resolution:</span> 1920x1080 (9:16)
            </div>
            <Badge variant="outline" className="font-normal">
              {selectedModels.length} Models × {selectedAngles.length} Angles = {totalImagesToGenerate} images/product
            </Badge>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-117px)]">
        {/* Left Sidebar - Product & Model Selection */}
        <div className="w-80 bg-white border-r flex flex-col">
          {/* Fixed Header */}
          <div className="p-4 border-b">
            <h3 className="font-medium text-sm">Selection</h3>
          </div>
          
          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-6">
              {/* Target Garment */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-sm font-medium">Target Garment</Label>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-6 text-xs"
                    onClick={() => setShowProductSelector(true)}
                  >
                    {selectedProduct ? 'Change' : 'Select'}
                  </Button>
                </div>
                {selectedProduct ? (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex gap-3">
                      {/* Show the selected image from V2 or first available */}
                      {v2Images.image_2 ? (
                        <div className="relative">
                          <img 
                            src={v2Images.image_2.preview} 
                            className="w-16 h-20 object-cover rounded" 
                            alt={selectedProduct.name}
                          />
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                            <CheckCircle className="w-3 h-3 text-white" />
                          </div>
                        </div>
                      ) : productAssets[0] ? (
                        <img 
                          src={getAssetUrl(productAssets[0], true)} 
                          className="w-16 h-20 object-cover rounded" 
                          alt={selectedProduct.name}
                          onError={(e) => {
                            // Fallback to compressed if thumbnail fails
                            e.currentTarget.src = getAssetUrl(productAssets[0], false);
                          }}
                        />
                      ) : (
                        <div className="w-16 h-20 bg-gray-200 rounded flex items-center justify-center">
                          <Sparkles className="w-4 h-4 text-gray-400" />
                        </div>
                      )}
                      <div className="flex-1">
                        <Badge variant="outline" className="text-xs mb-1">{selectedProduct.sku || 'No SKU'}</Badge>
                        <p className="text-sm font-medium line-clamp-1">{selectedProduct.name}</p>
                        {/* Show the actual prompt text being used */}
                        {uploadedGarments.find(g => g.id === `product-${selectedProduct.id}`) ? (
                          <p className="text-xs text-green-600 line-clamp-2 mt-1">
                            {uploadedGarments.find(g => g.id === `product-${selectedProduct.id}`)?.promptText}
                          </p>
                        ) : (
                          <p className="text-xs text-gray-600 line-clamp-2">{selectedProduct.description || 'No description'}</p>
                        )}
                        {v2Images.image_2 && (
                          <div className="mt-1 flex items-center gap-1">
                            <CheckCircle className="w-3 h-3 text-green-600" />
                            <p className="text-xs text-green-600">Ready for generation</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-3 border-2 border-dashed border-gray-300 rounded-lg text-center">
                    <p className="text-sm text-gray-500">No product selected</p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => setShowProductSelector(true)}
                    >
                      Select Product
                    </Button>
                  </div>
                )}
              </div>

              {/* Additional Garments */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Additional Garments</Label>
                <div className="grid grid-cols-3 gap-2">
                  {uploadedGarments.filter(g => !g.id.startsWith('product-')).map((garment) => (
                    <div
                      key={garment.id}
                      className={cn(
                        "border rounded-lg p-2 cursor-pointer transition-all text-center relative overflow-hidden",
                        selectedGarments.includes(garment.id)
                          ? "border-purple-600 bg-purple-50"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                      onClick={() => !garment.uploading && toggleGarment(garment.id)}
                    >
                      {garment.uploading && (
                        <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
                          <div className="flex flex-col items-center">
                            <div className="w-5 h-5 border-2 border-purple-600 border-t-transparent rounded-full animate-spin mb-1" />
                            <p className="text-xs text-purple-600">AI analyzing...</p>
                          </div>
                        </div>
                      )}
                      <div className="w-full h-16 rounded mb-1 overflow-hidden bg-gray-100">
                        <img 
                          src={garment.image} 
                          alt={garment.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <p className="text-xs font-medium line-clamp-1">{garment.name}</p>
                    </div>
                  ))}
                  
                  {/* Upload button */}
                  <label className="border border-dashed border-gray-300 rounded-lg p-2 cursor-pointer hover:border-gray-400 transition-colors">
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleGarmentUpload}
                      className="hidden"
                    />
                    <div className="w-full h-16 flex items-center justify-center">
                      <Upload className="w-5 h-5 text-gray-400" />
                    </div>
                    <p className="text-xs text-gray-400 text-center">Upload</p>
                  </label>
                  
                  {/* Empty slots */}
                  {[...Array(Math.max(0, 5 - uploadedGarments.filter(g => !g.id.startsWith('product-')).length))].map((_, i) => (
                    <div key={`placeholder-${i}`} className="border border-dashed border-gray-200 rounded-lg p-2 opacity-50">
                      <div className="w-full h-16 flex items-center justify-center">
                        <Plus className="w-4 h-4 text-gray-300" />
                      </div>
                      <p className="text-xs text-gray-300 text-center">Empty</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Models */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Models</Label>
                <div className="grid grid-cols-2 gap-2">
                  {campaignModels.map((model) => {
                    const modelCheck = checkModelImages(model.id);
                    const hasWarning = !modelCheck.hasFace;
                    
                    return (
                      <div
                        key={model.id}
                        className={cn(
                          "border rounded-lg cursor-pointer transition-all overflow-hidden relative",
                          selectedModels.includes(model.id)
                            ? "border-purple-600 bg-purple-50"
                            : "border-gray-200 hover:border-gray-300"
                        )}
                        onClick={() => toggleModel(model.id)}
                      >
                        {hasWarning && (
                          <div className="absolute top-1 right-1 z-10">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <AlertCircle className="w-4 h-4 text-yellow-500" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="text-xs">Missing face photo</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        )}
                        <ModelImage
                          src={model.image}
                          alt={model.name}
                          className="w-full h-32 object-cover"
                          fallbackSrc="placeholder.svg"
                        />
                        <div className="p-2">
                          <p className="text-xs font-medium text-center">{model.shortName}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Angles */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Angles</Label>
                <div className="grid grid-cols-2 gap-2">
                  {angleBlocks.map((angle) => {
                    const hasWarning = selectedModels.length > 0 && 
                      !dynamicModelImageMapping[selectedModels[0]]?.[angle.name];
                    
                    return (
                      <div
                        key={angle.id}
                        className={cn(
                          "border rounded-lg cursor-pointer transition-all overflow-hidden relative",
                          selectedAngles.includes(angle.id)
                            ? "border-green-600 bg-green-50"
                            : "border-gray-200 hover:border-gray-300"
                        )}
                        onClick={() => toggleAngle(angle.id)}
                      >
                        {hasWarning && (
                          <div className="absolute top-1 right-1 z-10">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <AlertCircle className="w-4 h-4 text-yellow-500" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="text-xs">Missing angle photo for selected model</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        )}
                        <img
                          src={`/${anglePreviewMapping[angle.name]}`}
                          alt={angle.name}
                          className="w-full h-28 object-cover"
                        />
                        <div className="p-1">
                          <p className="text-xs font-medium text-center">{angle.name}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Settings */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Settings</Label>
                <div className="space-y-3">
                  {settingBlocks.map((category) => (
                    <div key={category.id}>
                      <Label className="text-xs text-muted-foreground mb-1 flex items-center gap-1">
                        <span>{category.icon}</span>
                        {category.name}
                      </Label>
                      <div className="grid grid-cols-2 gap-1">
                        {category.options.map((option) => (
                          <Button
                            key={option.id}
                            variant={selectedSettings[category.id] === option.id ? "secondary" : "outline"}
                            size="sm"
                            className={cn(
                              "text-xs h-8",
                              selectedSettings[category.id] === option.id && "bg-gray-200"
                            )}
                            onClick={() => toggleSetting(category.id, option.id)}
                          >
                            {option.name}
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Selection Toolbar */}
          {v2GeneratedImages.length > 0 && (
            <div className="p-4 bg-white border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">
                    {selectedImages.length} of {v2GeneratedImages.length} selected
                  </span>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={selectAllImages}
                    disabled={isMovingImages}
                  >
                    Select All
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setSelectedImages([])}
                    disabled={isMovingImages || selectedImages.length === 0}
                  >
                    Clear Selection
                  </Button>
                </div>
                {selectedImages.length > 0 && (
                  <Button 
                    onClick={() => moveSelectedToCollection()} 
                    disabled={isMovingImages}
                  >
                    {isMovingImages ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Moving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Move to Collection ({selectedImages.length})
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          )}
          
          <div className="flex-1 overflow-auto">
              {/* Generated Images Grid - Organized by Model */}
              <div className="p-4">
                {v2GeneratedImages.length > 0 ? (
                  <div className="space-y-6">
                    {/* Generated Images */}
                    <div className="mb-6">
                      <h4 className="font-medium mb-3">Generated Images</h4>
                        <div className="grid grid-cols-4 gap-4">
                          {v2GeneratedImages.map((image: any) => (
                            <div key={image.id} className="relative">
                              <div className="relative group">
                                <img 
                                  src={`${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/ai-generated/${image.storage_path}`}
                                  alt="V2 Generated"
                                  className={cn(
                                    "w-full aspect-[9/16] object-cover rounded-lg cursor-pointer transition-all",
                                    selectedImages.includes(image.id) && "ring-2 ring-primary"
                                  )}
                                  onClick={() => {
                                    const imageIndex = allGeneratedImages.findIndex(img => img.id === image.id);
                                    if (imageIndex !== -1) {
                                      openFullScreenViewer(imageIndex);
                                    }
                                  }}
                                />
                                {/* Selection checkbox */}
                                <div 
                                  className="absolute top-2 left-2 z-10"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleImageSelection(image.id);
                                  }}
                                >
                                  <div className={cn(
                                    "w-6 h-6 rounded border-2 flex items-center justify-center transition-colors",
                                    selectedImages.includes(image.id) 
                                      ? "bg-primary border-primary" 
                                      : "bg-white/80 border-gray-400 hover:border-gray-600"
                                  )}>
                                    {selectedImages.includes(image.id) && (
                                      <Check className="w-4 h-4 text-white" />
                                    )}
                                  </div>
                                </div>
                                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                  <Button
                                    size="icon"
                                    variant="secondary"
                                    className="h-8 w-8"
                                    title="View full screen"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const imageIndex = allGeneratedImages.findIndex(img => img.id === image.id);
                                      if (imageIndex !== -1) {
                                        openFullScreenViewer(imageIndex);
                                      }
                                    }}
                                  >
                                    <Expand className="w-4 h-4" />
                                  </Button>
                                </div>
                                {image.selected && (
                                  <Badge className="absolute top-2 right-2 bg-green-500">
                                    Selected
                                  </Badge>
                                )}
                              </div>
                              <p className="text-xs mt-2 text-center">
                                {new Date(image.created_at).toLocaleTimeString()}
                              </p>
                            </div>
                          ))}
                        </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-96">
                    <div className="w-32 h-32 bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                      <Sparkles className="w-12 h-12 text-gray-400" />
                    </div>
                    <p className="text-gray-500 text-lg mb-2">No images generated yet</p>
                    <p className="text-gray-400 text-sm">Configure settings and click "Generate Images"</p>
                  </div>
                )}
              </div>
          </div>
        </div>

        {/* Right Sidebar - Generation Controls */}
        <div className="w-96 bg-white border-l flex flex-col">
          {/* Fixed Header */}
          <div className="p-4 border-b bg-white">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-sm">Prompt Builder</h3>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 text-xs"
                  onClick={() => setShowPromptHistory(!showPromptHistory)}
                >
                  <History className="w-3 h-3 mr-1" />
                  History
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 text-xs"
                  onClick={saveToHistory}
                >
                  <Star className="w-3 h-3 mr-1" />
                  Save
                </Button>
              </div>
            </div>
          </div>
          
          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-4">

              {/* Model Photo Uploads */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Model Photos</Label>
                <p className="text-xs text-muted-foreground mb-3">Upload photos to customize your model generation</p>
                <div className="grid grid-cols-2 gap-2">
                  {/* Face Image */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Model Face Photo</Label>
                    <div className="relative">
                      {v2Images.face ? (
                        <div className="relative group">
                          <img 
                            src={v2Images.face.preview} 
                            alt="Face preview"
                            className="w-full h-40 object-cover rounded border"
                          />
                          <button
                            onClick={() => removeV2Image('face')}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="w-3 h-3" />
                          </button>
                          <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1">
                            {v2Images.face.file.name}
                          </div>
                        </div>
                      ) : (
                        <label className="flex items-center justify-center w-full h-40 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400 transition-colors">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) handleV2ImageUpload('face', file);
                            }}
                            className="hidden"
                          />
                          <div className="text-center">
                            <Upload className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                            <p className="text-xs text-gray-500">Upload Face Photo</p>
                          </div>
                        </label>
                      )}
                    </div>
                  </div>
                  {/* Input Images 2-4 */}
                  {(['image_2', 'image_3', 'image_4'] as const).map((type, index) => (
                    <div key={type} className="space-y-1">
                      <Label className="text-xs font-medium">
                        {type === 'image_2' ? 'Model Body Shot' : type === 'image_3' ? 'Product Image' : 'Additional Item'}
                      </Label>
                      <div className="relative">
                        {v2Images[type] ? (
                          <div className="relative group">
                            <img 
                              src={v2Images[type]!.preview} 
                              alt={`Input ${index + 2} preview`}
                              className="w-full h-40 object-cover rounded border"
                            />
                            <button
                              onClick={() => removeV2Image(type)}
                              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <X className="w-3 h-3" />
                            </button>
                            <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-[10px] p-1 truncate">
                              {v2Images[type]!.file.name}
                            </div>
                          </div>
                        ) : (
                          <label className="flex items-center justify-center w-full h-40 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400 transition-colors">
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) handleV2ImageUpload(type, file);
                              }}
                              className="hidden"
                            />
                            <div className="text-center">
                              <Upload className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                              <p className="text-xs text-gray-500">
                              {type === 'image_2' ? 'Model Body Shot' : type === 'image_3' ? 'Product Image' : 'Additional Item'}
                            </p>
                            </div>
                          </label>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                {v2Images.face && v2Images.image_2 && v2Images.image_3 && v2Images.image_4 && (
                  <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded">
                    <p className="text-xs text-green-700">✓ All photos uploaded successfully</p>
                  </div>
                )}
              </div>

              {/* Flexible Input Box for Extra Elements */}
              <FlexibleInputBox
                inputs={flexibleInputs}
                onInputsChange={setFlexibleInputs}
                maxInputs={10}
                className="mt-4"
              />

              {/* Active Blocks Display */}
              {activeBlocks.length > 0 && (
                <div>
                  <Label className="text-xs text-muted-foreground mb-2 block">Active Blocks</Label>
                  <div className="flex flex-wrap gap-1.5">
                    {activeBlocks.map((block, index) => {
                      const colors = BLOCK_COLORS[block.type] || BLOCK_COLORS.model;
                      return (
                        <Badge
                          key={index}
                          variant="secondary"
                          className={cn(
                            "text-xs px-2 py-1 cursor-pointer border transition-all",
                            colors.bg,
                            colors.text,
                            colors.border,
                            "hover:opacity-80"
                          )}
                        >
                          {block.name}
                          <X
                            className="w-3 h-3 ml-1 opacity-60 hover:opacity-100"
                            onClick={() => removeBlockFromPrompt(block.text, block.type)}
                          />
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Main Prompt Area */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-1">
                    <Label className="text-sm font-medium">Prompt Builder</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                            <Info className="h-3 w-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-xs">
                          <div className="space-y-1 text-xs">
                            <p className="font-medium mb-1">Prompt Structure:</p>
                            <p>1. <span className="font-medium">Place/Create</span> + <span className="font-medium">Model</span> description</p>
                            <p>2. <span className="font-medium">wearing this main garment</span> + Featured product</p>
                            <p>3. <span className="font-medium">styled with</span> + Additional garments</p>
                            <p>4. <span className="font-medium">on this</span> + Background/Setting</p>
                            <p>5. <span className="font-medium">Angle</span> + Camera position</p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 text-xs"
                    onClick={() => {
                      // Clear everything and rebuild prompt
                      setActiveBlocks([]);
                      setSelectedModels([]);
                      setSelectedAngles([]);
                      setSelectedGarments([]);
                      setSelectedSettings({});
                      buildNewStylePrompt();
                    }}
                  >
                    Clear Blocks
                  </Button>
                </div>
                {/* Prompt editor with visual highlights */}
                <div className="relative">
                  {/* Visual display of prompt with highlights */}
                  <div className="relative group">
                    <div 
                      className={cn(
                        "min-h-[200px] max-h-[400px] overflow-y-auto text-base p-4 pr-20 border-2 rounded-lg bg-white cursor-text leading-relaxed transition-all hover:border-primary/50 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100",
                        isEditingPrompt && "border-primary ring-2 ring-primary/20"
                      )}
                      onClick={() => {
                        // Focus the hidden textarea when clicking the display
                        const textarea = document.getElementById('prompt-textarea');
                        if (textarea) textarea.focus();
                      }}
                    >
                      {settings.prompt ? (
                        <div className="whitespace-pre-wrap break-words">
                          {parsePromptSegments(settings.prompt).map((segment, index) => (
                            segment.type ? (
                              <span
                                key={index}
                                className={cn(
                                  "relative",
                                  BLOCK_COLORS[segment.type]?.bg,
                                  BLOCK_COLORS[segment.type]?.text,
                                  "px-0.5 rounded-sm"
                                )}
                                style={{
                                  backgroundColor: segment.type === 'model' ? 'rgba(59, 130, 246, 0.2)' :
                                                   segment.type === 'angle' ? 'rgba(16, 185, 129, 0.2)' :
                                                   segment.type === 'product' ? 'rgba(99, 102, 241, 0.2)' :
                                                   segment.type === 'garment' ? 'rgba(139, 92, 246, 0.2)' :
                                                   segment.type === 'background' ? 'rgba(251, 146, 60, 0.2)' :
                                                   segment.type === 'artDirection' ? 'rgba(236, 72, 153, 0.2)' :
                                                   'transparent'
                                }}
                              >
                                {segment.text}
                              </span>
                            ) : (
                              <span key={index}>{segment.text}</span>
                            )
                          ))}
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-base">Build your prompt by selecting blocks or type directly...</span>
                      )}
                    </div>
                    
                    {/* Overlay textarea for editing */}
                    <Textarea
                      id="prompt-textarea"
                      className="absolute inset-0 resize-none bg-transparent p-4 pr-20 text-base leading-relaxed focus:outline-none cursor-text"
                      style={{ 
                        caretColor: 'rgb(37, 99, 235)', // Blue cursor
                        color: 'rgba(0, 0, 0, 0.05)', // Very faint text to help with cursor positioning
                        WebkitTextFillColor: 'rgba(0, 0, 0, 0.05)',
                        caretWidth: '2px'
                      }}
                      value={settings.prompt}
                      onChange={(e) => {
                        setSettings({ ...settings, prompt: e.target.value });
                        // Don't rebuild prompt on manual edits
                      }}
                      onFocus={() => setIsEditingPrompt(true)}
                      onBlur={() => {
                        setIsEditingPrompt(false);
                        // Optionally sync active blocks with manual edits
                      }}
                      placeholder=""
                      spellCheck={false}
                    />
                    
                    <div className="absolute top-3 right-3 bg-white/95 backdrop-blur-sm rounded-md px-2.5 py-1.5 shadow-sm border border-gray-100 pointer-events-none">
                      <div className="text-xs text-muted-foreground">
                        {isEditingPrompt ? (
                          <span className="text-primary font-medium flex items-center gap-1">
                            <span className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse" />
                            Editing
                          </span>
                        ) : (
                          <>
                            <span className="font-medium text-primary">{activeBlocks.length}</span> blocks
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Info and legend */}
                  <div className="mt-2 space-y-2">
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{isEditingPrompt ? 'Type to edit prompt • Blue cursor shows position' : 'Click prompt to edit • Highlighted text = AI blocks'}</span>
                      <span>{settings.prompt.length} chars</span>
                    </div>
                    <div className="flex items-center gap-2 flex-wrap text-xs">
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(59, 130, 246, 0.2)' }} />
                        <span>Model</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(16, 185, 129, 0.2)' }} />
                        <span>Angle</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(99, 102, 241, 0.2)' }} />
                        <span>Product</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(139, 92, 246, 0.2)' }} />
                        <span>Garment</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(251, 146, 60, 0.2)' }} />
                        <span>Background</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(236, 72, 153, 0.2)' }} />
                        <span>Style</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>


              {/* Generation Requirements Status */}
              <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <Label className="text-sm font-medium mb-2 block">Ready to Generate?</Label>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-xs">
                    {selectedModels.length > 0 ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-gray-400" />
                    )}
                    <span className={selectedModels.length > 0 ? "text-green-700" : "text-gray-500"}>
                      Model selected
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    {selectedAngles.length > 0 ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-gray-400" />
                    )}
                    <span className={selectedAngles.length > 0 ? "text-green-700" : "text-gray-500"}>
                      Angle selected
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    {selectedProduct ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-gray-400" />
                    )}
                    <span className={selectedProduct ? "text-green-700" : "text-gray-500"}>
                      Product selected
                    </span>
                  </div>
                  {selectedModels.length > 0 && (() => {
                    const modelCheck = checkModelImages(selectedModels[0]);
                    return (
                      <>
                        <div className="flex items-center gap-2 text-xs">
                          {modelCheck.hasFace ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <AlertCircle className="w-4 h-4 text-yellow-500" />
                          )}
                          <span className={modelCheck.hasFace ? "text-green-700" : "text-yellow-700"}>
                            Model face photo {modelCheck.hasFace ? "available" : "missing"}
                          </span>
                        </div>
                        {selectedAngles.length > 0 && (
                          <div className="flex items-center gap-2 text-xs">
                            {modelCheck.hasAngle ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <AlertCircle className="w-4 h-4 text-yellow-500" />
                            )}
                            <span className={modelCheck.hasAngle ? "text-green-700" : "text-yellow-700"}>
                              Angle photo {modelCheck.hasAngle ? "available" : "missing"}
                            </span>
                          </div>
                        )}
                      </>
                    );
                  })()}
                </div>
              </div>

              {/* Technical Settings */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Technical Settings</Label>
                <div className="space-y-3">
                  {/* Number of Images */}
                  <div>
                    <Label className="text-xs text-muted-foreground">Number of Images</Label>
                    <Select
                      value={settings.numImages.toString()}
                      onValueChange={(v) => setSettings({ ...settings, numImages: parseInt(v) })}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[1, 2, 4, 8].map(num => (
                          <SelectItem key={num} value={num.toString()}>
                            {num} {num === 1 ? 'image' : 'images'} per combination
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Aspect Ratio */}
                  <div>
                    <Label className="text-xs text-muted-foreground">Aspect Ratio</Label>
                    <Select 
                      value={settings.ratio} 
                      onValueChange={(v) => {
                        setSettings({ ...settings, ratio: v });
                        updateTechnicalSettingInPrompt('ratio', v);
                      }}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="9:16">9:16 (Portrait)</SelectItem>
                        <SelectItem value="16:9">16:9 (Landscape)</SelectItem>
                        <SelectItem value="1:1">1:1 (Square)</SelectItem>
                        <SelectItem value="4:5">4:5 (Social)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Image Format */}
                  <div>
                    <Label className="text-xs text-muted-foreground">Image Format</Label>
                    <Select 
                      value={settings.imageFormat} 
                      onValueChange={(v) => {
                        setSettings({ ...settings, imageFormat: v });
                        updateTechnicalSettingInPrompt('format', v);
                      }}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="jpeg">JPEG (smaller size)</SelectItem>
                        <SelectItem value="png">PNG (transparency)</SelectItem>
                        <SelectItem value="webp">WebP (modern)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Advanced Settings Toggle */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-xs"
                    onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                  >
                    <ChevronDown className={cn("w-3 h-3 mr-1 transition-transform", showAdvancedSettings && "rotate-180")} />
                    Advanced Settings
                  </Button>
                  
                  {showAdvancedSettings && (
                    <div className="space-y-3 pt-2">
                      {/* Seed */}
                      <div>
                        <Label className="text-xs text-muted-foreground">Seed (leave empty for random)</Label>
                        <Input
                          type="number"
                          placeholder="Random"
                          className="h-8 text-sm"
                          value={settings.seed || ''}
                          onChange={(e) => {
                            const seed = e.target.value ? parseInt(e.target.value) : null;
                            setSettings({ ...settings, seed });
                            updateTechnicalSettingInPrompt('seed', seed);
                          }}
                        />
                      </div>
                      
                      {/* CFG Scale */}
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <Label className="text-xs text-muted-foreground">CFG Scale</Label>
                          <span className="text-xs font-medium">{settings.cfg}</span>
                        </div>
                        <Slider
                          value={[settings.cfg]}
                          onValueChange={([value]) => {
                            setSettings({ ...settings, cfg: value });
                            updateTechnicalSettingInPrompt('cfg', value);
                          }}
                          min={1}
                          max={20}
                          step={0.5}
                          className="h-1"
                        />
                      </div>
                    </div>
                  )}
                  
                  <div className="pt-2 border-t flex items-center justify-between text-xs text-muted-foreground">
                    <span>Total to generate:</span>
                    <span className="font-medium">{totalImagesToGenerate * settings.numImages} images</span>
                  </div>
                </div>
              </div>

              {/* Removed V2 API Image Uploads - moved above */}
            </div>
          </div>

          {/* Fixed Footer with Generate Button */}
          <div className="p-4 border-t bg-white">
            <Button
              onClick={generateBatch}
              disabled={
                isGeneratingV2 ||
                (selectedModels.length === 0 || selectedAngles.length === 0) &&
                !(v2Images.face && v2Images.image_2 && v2Images.image_3 && v2Images.image_4)
              }
              className="w-full"
              size="lg"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              {v2Images.face && v2Images.image_2 && v2Images.image_3 && v2Images.image_4
                ? 'Generate with V2 API'
                : `Generate ${totalImagesToGenerate} Images`
              }
            </Button>
            
            {/* Debug info - remove in production */}
            {(selectedModels.length === 0 || selectedAngles.length === 0) && (
              <div className="mt-2 text-xs text-gray-500">
                {!v2Images.face && selectedModels.length === 0 && <p>• Select at least one model or upload V2 images</p>}
                {!v2Images.face && selectedAngles.length === 0 && <p>• Select at least one angle or upload V2 images</p>}
              </div>
            )}
            
            {/* Progress */}
            {isGeneratingV2 && (
              <div className="mt-2 space-y-1">
                <Progress value={progressV2} className="h-2" />
                <p className="text-xs text-center text-gray-600">
                  Processing V2 API request...
                </p>
              </div>
            )}
            

          </div>
        </div>
      </div>

      {/* Prompt History Dialog */}
      <Dialog open={showPromptHistory} onOpenChange={setShowPromptHistory}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Prompt History</DialogTitle>
          </DialogHeader>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {promptHistory.length > 0 ? (
              promptHistory.map((prompt, index) => (
                <Card key={index} className="p-3 cursor-pointer hover:bg-gray-50" onClick={() => {
                  setSettings({ ...settings, prompt });
                  setShowPromptHistory(false);
                }}>
                  <p className="text-sm line-clamp-2">{prompt}</p>
                </Card>
              ))
            ) : (
              <p className="text-sm text-muted-foreground text-center py-8">No saved prompts yet</p>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Full Screen Image Viewer */}
      <FullScreenImageViewer
        images={allGeneratedImages}
        currentIndex={fullScreenIndex}
        isOpen={isFullScreenOpen}
        onClose={closeFullScreenViewer}
        onNavigate={navigateFullScreenImage}
      />

      {/* Product Selector Dialog */}
      <Dialog open={showProductSelector} onOpenChange={setShowProductSelector}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Select Product</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            {productsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-2">No products found in this collection</p>
                <p className="text-sm text-gray-400">Create products to use them in the AI generator</p>
              </div>
            ) : (
              <div className="grid grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto">
                {products.map((product: any) => {
                  const isSelected = selectedProduct?.id === product.id;
                  return (
                    <div
                      key={product.id}
                      className={cn(
                        "border rounded-lg p-4 cursor-pointer transition-all",
                        isSelected
                          ? "border-primary bg-primary/5"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                      onClick={() => {
                        // Clear existing V2 image if changing product
                        if (v2Images.image_2) {
                          removeV2Image('image_2');
                        }
                        
                        setSelectedProduct(product);
                        setSelectedProductId(product.id);
                        setShowProductSelector(false);
                        
                        // Product assets will be auto-loaded via useEffect
                      }}
                    >
                      <div className="aspect-square bg-gray-100 rounded mb-3 overflow-hidden">
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          <Sparkles className="w-8 h-8" />
                        </div>
                      </div>
                      <div>
                        <p className="font-medium text-sm line-clamp-1">{product.name}</p>
                        <p className="text-xs text-gray-500 mt-1">{product.sku || 'No SKU'}</p>
                        <p className="text-xs text-gray-400 mt-1">View assets</p>
                      </div>
                      {isSelected && (
                        <div className="absolute top-2 right-2">
                          <Check className="w-5 h-5 text-primary" />
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Image Selector Dialog for Product */}
      <Dialog open={showImageSelector} onOpenChange={setShowImageSelector}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Select Product Image</DialogTitle>
            <DialogDescription>
              Choose which image of {selectedProduct?.name} to use as the main garment
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            {productAssets.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No images found for this product</p>
              </div>
            ) : (
              <div className="grid grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto">
                {productAssets.map((asset: any) => {
                  const assetUrl = getAssetUrl(asset, true); // Use thumbnail for preview
                  const hasAIDescription = asset.metadata?.ai_description || asset.metadata?.description;
                  
                  return (
                    <div
                      key={asset.id}
                      className="border rounded-lg p-3 cursor-pointer hover:border-primary transition-all group"
                      onClick={() => handleProductImageSelection(asset)}
                    >
                      <div className="aspect-square bg-gray-100 rounded mb-2 overflow-hidden">
                        <img 
                          src={assetUrl} 
                          alt={asset.file_name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                        />
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium truncate">{asset.file_name}</p>
                        {hasAIDescription && (
                          <div className="flex items-center gap-1 text-xs text-green-600">
                            <CheckCircle className="w-3 h-3" />
                            <span>AI analyzed</span>
                          </div>
                        )}
                        {asset.metadata?.view && (
                          <Badge variant="secondary" className="text-xs">
                            {asset.metadata.view}
                          </Badge>
                        )}
                        <p className="text-xs text-gray-500 line-clamp-2">
                          {asset.metadata?.ai_description || asset.metadata?.description || 'No description available'}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowImageSelector(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}