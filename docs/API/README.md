# Custom ComfyUI Serverless Endpoint - Team Deliverables

## Project Overview

We have successfully implemented a **custom ComfyUI serverless endpoint** on RunPod with our own models and custom nodes. This endpoint is production-ready and handles complex AI image generation workflows with advanced features.

---

## Key Achievements

- **Custom Docker Image**: `docker.io/fashion2025/my-comfyui-models:v3`
- **Production Endpoint**: `https://api.runpod.ai/v2/s426ojl4svhd9e/run`
- **Complex Workflow Support**: Advanced FLUX models with custom nodes
- **True Serverless Architecture**: Auto-scaling based on demand
- **Custom Models & Nodes**: Integrated via multiple installation methods

---

## Technical Architecture

### Base Implementation
- **Source**: Modified official `runpod/worker-comfyui` Dockerfile
- **Base Image**: `runpod/worker-comfyui:latest`
- **Custom Image**: `docker.io/fashion2025/my-comfyui-models:v3`

### Customization Strategy
We used a **hybrid approach** combining automated and manual installation methods:

#### 1. **Automated Installation (Dockerfile)**
```dockerfile
# WAS Node Suite - Installed automatically during build
RUN git clone https://github.com/WASasquatch/was-node-suite-comfyui.git custom_nodes/was-node-suite-comfyui
RUN pip install -r custom_nodes/was-node-suite-comfyui/requirements.txt
RUN chmod -R 755 custom_nodes/was-node-suite-comfyui
```

#### 2. **Manual Installation (Post-Build)**
- **Custom Nodes**: Installed via ComfyUI UI interface
- **Custom Models**: Installed via `docker exec` commands on running container

---

## Available Models & Nodes

### Models Available
- `epicrealismXL_vxviLastfameRealism.safetensors`
- `fluxToolsRedux_reduxDev.safetensors`
- Plus all custom models installed via `docker exec`

### Custom Nodes Available
- **WAS Node Suite** (automated installation)
- **PuLID Flux** (manual installation)
- **Impact Suite** (manual installation)
- **All other custom nodes** (manual installation via UI)

---

## Production Endpoint Details

### Endpoint Information
- **URL**: `https://api.runpod.ai/v2/s426ojl4svhd9e/run`
- **Type**: RunPod Serverless Endpoint
- **Status**: **Production Ready**
- **Architecture**: True serverless with auto-scaling

### API Endpoints Available
- `/run` - Asynchronous job submission
- `/runsync` - Synchronous job submission (recommended for testing)
- `/health` - Health check endpoint

---

## 📋 Request/Response Format

### Input Format
```json
{
  "input": {
    "images": [
      {
        "name": "b0f348d5-ece9-4dca-b1f5-83c2e6d560a8.png",
        "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAIAAAD8GO2jAAAAMklEQVR4nGI5ZdXAQEvARFPTRy0YtWDUglELRi0YtWDUglELRi0YtWDUAioCQAAAAP//E24Bx3jUKuYAAAAASUVORK5CYII="
      },
      {
        "name": "xs10.png",
        "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAIAAAD8GO2jAAAAMklEQVR4nGI5ZdXAQEvARFPTRy0YtWDUglELRi0YtWDUglELRi0YtWDUAioCQAAAAP//E24Bx3jUKuYAAAAASUVORK5CYII="
      },
      {
        "name": "xs6 (1).png",
        "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAIAAAD8GO2jAAAAMklEQVR4nGI5ZdXAQEvARFPTRy0YtWDUglELRi0YtWDUglELRi0YtWDUAioCQAAAAP//E24Bx3jUKuYAAAAASUVORK5CYII="
      },
      {
        "name": "6dc55d53-3379-4b11-ad01-a2986cca12db.webp",
        "image": "data:image/webp;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAIAAAD8GO2jAAAAMklEQVR4nGI5ZdXAQEvARFPTRy0YtWDUglELRi0YtWDUglELRi0YtWDUAioCQAAAAP//E24Bx3jUKuYAAAAASUVORK5CYII="
      }
    ],
    "workflow": {
      "10": {
        "inputs": {
          "vae_name": "ae.safetensors"
        },
        "class_type": "VAELoader",
        "_meta": {
          "title": "Load VAE"
        }
      },
      "11": {
        "inputs": {
          "clip_name1": "ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors",
          "clip_name2": "t5xxl_fp8_e4m3fn_scaled.safetensors",
          "type": "flux",
          "device": "default"
        },
        "class_type": "DualCLIPLoader",
        "_meta": {
          "title": "DualCLIPLoader"
        }
      },
      "12": {
        "inputs": {
          "unet_name": "fluxFillFP8_v10.safetensors",
          "weight_dtype": "fp8_e4m3fn"
        },
        "class_type": "UNETLoader",
        "_meta": {
          "title": "Load Diffusion Model"
        }
      },
      "102": {
        "inputs": {
          "seed": [
            "424",
            3
          ],
          "steps": 20,
          "cfg": 1,
          "sampler_name": "euler",
          "scheduler": "beta",
          "denoise": [
            "1040",
            0
          ],
          "model": [
            "652",
            0
          ],
          "positive": [
            "657",
            0
          ],
          "negative": [
            "220",
            1
          ],
          "latent_image": [
            "220",
            2
          ]
        },
        "class_type": "KSampler",
        "_meta": {
          "title": "KSampler"
        }
      },
      "103": {
        "inputs": {
          "text": [
            "1027",
            0
          ],
          "clip": [
            "922",
            1
          ]
        },
        "class_type": "CLIPTextEncode",
        "_meta": {
          "title": "CLIP Text Encode (Prompt)"
        }
      },
      "104": {
        "inputs": {
          "text": "",
          "clip": [
            "922",
            1
          ]
        },
        "class_type": "CLIPTextEncode",
        "_meta": {
          "title": "CLIP Text Encode (Prompt)"
        }
      },
      "106": {
        "inputs": {
          "samples": [
            "102",
            0
          ],
          "vae": [
            "10",
            0
          ]
        },
        "class_type": "VAEDecode",
        "_meta": {
          "title": "VAE Decode"
        }
      },
      "171": {
        "inputs": {
          "strength": 1,
          "strength_type": "multiply",
          "conditioning": [
            "220",
            0
          ],
          "style_model": [
            "173",
            0
          ],
          "clip_vision_output": [
            "172",
            0
          ]
        },
        "class_type": "StyleModelApply",
        "_meta": {
          "title": "Apply Style Model"
        }
      },
      "172": {
        "inputs": {
          "crop": "center",
          "clip_vision": [
            "1046",
            0
          ],
          "image": [
            "381",
            0
          ]
        },
        "class_type": "CLIPVisionEncode",
        "_meta": {
          "title": "CLIP Vision Encode"
        }
      },
      "173": {
        "inputs": {
          "style_model_name": "fluxToolsRedux_reduxDev.safetensors"
        },
        "class_type": "StyleModelLoader",
        "_meta": {
          "title": "Load Style Model"
        }
      },
      "220": {
        "inputs": {
          "noise_mask": true,
          "positive": [
            "103",
            0
          ],
          "negative": [
            "104",
            0
          ],
          "vae": [
            "10",
            0
          ],
          "pixels": [
            "417",
            0
          ],
          "mask": [
            "327",
            0
          ]
        },
        "class_type": "InpaintModelConditioning",
        "_meta": {
          "title": "InpaintModelConditioning"
        }
      },
      "271": {
        "inputs": {
          "width": [
            "417",
            2
          ],
          "height": [
            "417",
            3
          ],
          "x": [
            "417",
            4
          ],
          "y": [
            "417",
            5
          ],
          "image": [
            "106",
            0
          ]
        },
        "class_type": "ImageCrop",
        "_meta": {
          "title": "Image Crop"
        }
      },
      "291": {
        "inputs": {
          "upscale_method": "lanczos",
          "scale_by": [
            "613",
            5
          ],
          "image": [
            "981",
            0
          ]
        },
        "class_type": "ImageScaleBy",
        "_meta": {
          "title": "Upscale Image By"
        }
      },
      "326": {
        "inputs": {
          "expand": [
            "1042",
            0
          ],
          "incremental_expandrate": 0,
          "tapered_corners": true,
          "flip_input": false,
          "blur_radius": [
            "1041",
            0
          ],
          "lerp_alpha": 1,
          "decay_factor": 1,
          "fill_holes": false,
          "mask": [
            "613",
            7
          ]
        },
        "class_type": "GrowMaskWithBlur",
        "_meta": {
          "title": "Grow Mask With Blur"
        }
      },
      "327": {
        "inputs": {
          "expand": [
            "1042",
            0
          ],
          "incremental_expandrate": 0,
          "tapered_corners": true,
          "flip_input": false,
          "blur_radius": [
            "1041",
            0
          ],
          "lerp_alpha": 1,
          "decay_factor": 1,
          "fill_holes": false,
          "mask": [
            "417",
            1
          ]
        },
        "class_type": "GrowMaskWithBlur",
        "_meta": {
          "title": "Grow Mask With Blur"
        }
      },
      "342": {
        "inputs": {
          "x": [
            "613",
            3
          ],
          "y": [
            "613",
            4
          ],
          "resize_source": false,
          "destination": [
            "1066",
            0
          ],
          "source": [
            "291",
            0
          ],
          "mask": [
            "326",
            0
          ]
        },
        "class_type": "ImageCompositeMasked",
        "_meta": {
          "title": "ImageCompositeMasked"
        }
      },
      "364": {
        "inputs": {
          "text": "",
          "clip": [
            "922",
            1
          ]
        },
        "class_type": "CLIPTextEncode",
        "_meta": {
          "title": "CLIP Text Encode (Prompt)"
        }
      },
      "366": {
        "inputs": {
          "text": "",
          "clip": [
            "922",
            1
          ]
        },
        "class_type": "CLIPTextEncode",
        "_meta": {
          "title": "CLIP Text Encode (Prompt)"
        }
      },
      "370": {
        "inputs": {
          "seed": [
            "424",
            3
          ],
          "steps": 1,
          "cfg": 1,
          "sampler_name": "euler",
          "scheduler": "beta",
          "denoise": 0.1,
          "model": [
            "652",
            0
          ],
          "positive": [
            "364",
            0
          ],
          "negative": [
            "366",
            0
          ],
          "latent_image": [
            "373",
            0
          ]
        },
        "class_type": "KSampler",
        "_meta": {
          "title": "KSampler"
        }
      },
      "373": {
        "inputs": {
          "pixels": [
            "271",
            0
          ],
          "vae": [
            "10",
            0
          ]
        },
        "class_type": "VAEEncode",
        "_meta": {
          "title": "VAE Encode"
        }
      },
      "376": {
        "inputs": {
          "samples": [
            "370",
            0
          ],
          "vae": [
            "10",
            0
          ]
        },
        "class_type": "VAEDecode",
        "_meta": {
          "title": "VAE Decode"
        }
      },
      "381": {
        "inputs": {
          "x": 0,
          "y": 0,
          "resize_source": false,
          "destination": [
            "382",
            0
          ],
          "source": [
            "614",
            0
          ],
          "mask": [
            "389",
            0
          ]
        },
        "class_type": "ImageCompositeMasked",
        "_meta": {
          "title": "ImageCompositeMasked"
        }
      },
      "382": {
        "inputs": {
          "width": [
            "1035",
            2
          ],
          "height": [
            "1035",
            1
          ],
          "batch_size": 1,
          "color": 0
        },
        "class_type": "EmptyImage",
        "_meta": {
          "title": "EmptyImage"
        }
      },
      "389": {
        "inputs": {
          "expand": 0,
          "incremental_expandrate": 0,
          "tapered_corners": true,
          "flip_input": false,
          "blur_radius": 0,
          "lerp_alpha": 1,
          "decay_factor": 1,
          "fill_holes": false,
          "mask": [
            "614",
            1
          ]
        },
        "class_type": "GrowMaskWithBlur",
        "_meta": {
          "title": "Grow Mask With Blur"
        }
      },
      "417": {
        "inputs": {
          "patch_mode": [
            "461",
            0
          ],
          "patch_type": [
            "462",
            0
          ],
          "output_length": [
            "1044",
            0
          ],
          "patch_color": "#FF0000",
          "first_image": [
            "381",
            0
          ],
          "second_image": [
            "613",
            0
          ],
          "second_mask": [
            "613",
            1
          ]
        },
        "class_type": "ConcatContextWindow",
        "_meta": {
          "title": "Concatenate Context Window"
        }
      },
      "424": {
        "inputs": {
          "seed": 372138690439008
        },
        "class_type": "Seed",
        "_meta": {
          "title": "Seed"
        }
      },
      "451": {
        "inputs": {
          "guidance": 50,
          "conditioning": [
            "171",
            0
          ]
        },
        "class_type": "FluxGuidance",
        "_meta": {
          "title": "FluxGuidance"
        }
      },
      "461": {
        "inputs": {
          "text": [
            "631",
            0
          ]
        },
        "class_type": "CR String To Combo",
        "_meta": {
          "title": "Patch Mode"
        }
      },
      "462": {
        "inputs": {
          "text": [
            "631",
            1
          ]
        },
        "class_type": "CR String To Combo",
        "_meta": {
          "title": "Patch Type"
        }
      },
      "581": {
        "inputs": {
          "filename_prefix": "ComfyUI",
          "images": [
            "342",
            0
          ]
        },
        "class_type": "SaveImage",
        "_meta": {
          "title": "Save Image"
        }
      },
      "613": {
        "inputs": {
          "patch_mode": [
            "461",
            0
          ],
          "patch_type": [
            "462",
            0
          ],
          "output_length": [
            "1044",
            0
          ],
          "pixel_buffer": [
            "1043",
            0
          ],
          "input_image": [
            "1066",
            0
          ],
          "input_mask": [
            "790",
            0
          ]
        },
        "class_type": "CreateContextWindow",
        "_meta": {
          "title": "Create Context Window"
        }
      },
      "614": {
        "inputs": {
          "patch_mode": [
            "461",
            0
          ],
          "patch_type": [
            "462",
            0
          ],
          "output_length": [
            "1044",
            0
          ],
          "pixel_buffer": [
            "1043",
            0
          ],
          "input_image": [
            "1064",
            0
          ],
          "input_mask": [
            "818",
            0
          ]
        },
        "class_type": "CreateContextWindow",
        "_meta": {
          "title": "Create Context Window"
        }
      },
      "631": {
        "inputs": {
          "image2": [
            "1066",
            0
          ],
          "mask2": [
            "790",
            0
          ]
        },
        "class_type": "AutoPatch",
        "_meta": {
          "title": "Auto Patch"
        }
      },
      "647": {
        "inputs": {
          "pulid_file": "pulid_flux_v0.9.1.safetensors"
        },
        "class_type": "PulidFluxModelLoader",
        "_meta": {
          "title": "Load PuLID Flux Model"
        }
      },
      "648": {
        "inputs": {},
        "class_type": "PulidFluxEvaClipLoader",
        "_meta": {
          "title": "Load Eva Clip (PuLID Flux)"
        }
      },
      "649": {
        "inputs": {
          "provider": "CUDA"
        },
        "class_type": "PulidFluxInsightFaceLoader",
        "_meta": {
          "title": "Load InsightFace (PuLID Flux)"
        }
      },
      "652": {
        "inputs": {
          "weight": 1.0000000000000002,
          "start_at": 0.4000000000000001,
          "end_at": 1,
          "model": [
            "922",
            0
          ],
          "pulid_flux": [
            "647",
            0
          ],
          "eva_clip": [
            "648",
            0
          ],
          "face_analysis": [
            "649",
            0
          ],
          "image": [
            "614",
            0
          ]
        },
        "class_type": "ApplyPulidFlux",
        "_meta": {
          "title": "Apply PuLID Flux"
        }
      },
      "657": {
        "inputs": {
          "union_controlnet_type": "pose",
          "strength": 0.9000000000000001,
          "start_percent": 0,
          "end_percent": 0.6500000000000001,
          "conditioning": [
            "451",
            0
          ],
          "control_net": [
            "658",
            0
          ],
          "image": [
            "665",
            0
          ],
          "vae": [
            "10",
            0
          ]
        },
        "class_type": "FluxUnionControlNetApply",
        "_meta": {
          "title": "Flux Union ControlNet Apply"
        }
      },
      "658": {
        "inputs": {
          "control_net_name": "shakker_flux_union_pro_20.safetensors"
        },
        "class_type": "ControlNetLoader",
        "_meta": {
          "title": "Load ControlNet Model"
        }
      },
      "665": {
        "inputs": {
          "detect_hand": "enable",
          "detect_body": "enable",
          "detect_face": "enable",
          "resolution": 1024,
          "bbox_detector": "yolox_l.onnx",
          "pose_estimator": "dw-ll_ucoco_384_bs5.torchscript.pt",
          "scale_stick_for_xinsr_cn": "disable",
          "image": [
            "417",
            0
          ]
        },
        "class_type": "DWPreprocessor",
        "_meta": {
          "title": "DWPose Estimator"
        }
      },
      "666": {
        "inputs": {
          "images": [
            "665",
            0
          ]
        },
        "class_type": "PreviewImage",
        "_meta": {
          "title": "Preview Image"
        }
      },
      "667": {
        "inputs": {
          "purge_cache": true,
          "purge_models": true,
          "anything": [
            "106",
            0
          ]
        },
        "class_type": "LayerUtility: PurgeVRAM",
        "_meta": {
          "title": "LayerUtility: Purge VRAM"
        }
      },
      "740": {
        "inputs": {
          "model_name": "bbox/face_yolov8m.pt"
        },
        "class_type": "UltralyticsDetectorProvider",
        "_meta": {
          "title": "UltralyticsDetectorProvider"
        }
      },
      "741": {
        "inputs": {
          "model_name": "sam_vit_b_01ec64.pth",
          "device_mode": "AUTO"
        },
        "class_type": "SAMLoader",
        "_meta": {
          "title": "SAMLoader (Impact)"
        }
      },
      "742": {
        "inputs": {
          "model_name": "segm/person_yolov8m-seg.pt"
        },
        "class_type": "UltralyticsDetectorProvider",
        "_meta": {
          "title": "UltralyticsDetectorProvider"
        }
      },
      "786": {
        "inputs": {
          "bbox_threshold": 0.5,
          "bbox_dilation": 10,
          "crop_factor": 3,
          "drop_size": 10,
          "sub_threshold": 0.9300000000000002,
          "sub_dilation": 5,
          "sub_bbox_expansion": 0,
          "sam_mask_hint_threshold": 0.7,
          "post_dilation": 0,
          "bbox_detector": [
            "740",
            0
          ],
          "image": [
            "1066",
            0
          ],
          "sam_model_opt": [
            "741",
            0
          ],
          "segm_detector_opt": [
            "742",
            1
          ]
        },
        "class_type": "ImpactSimpleDetectorSEGS",
        "_meta": {
          "title": "Simple Detector (SEGS)"
        }
      },
      "790": {
        "inputs": {
          "segs": [
            "786",
            0
          ]
        },
        "class_type": "SegsToCombinedMask",
        "_meta": {
          "title": "SEGS to MASK (combined)"
        }
      },
      "818": {
        "inputs": {
          "segs": [
            "865",
            0
          ]
        },
        "class_type": "SegsToCombinedMask",
        "_meta": {
          "title": "SEGS to MASK (combined)"
        }
      },
      "865": {
        "inputs": {
          "threshold": 0.5,
          "dilation": 10,
          "crop_factor": 3,
          "drop_size": 10,
          "labels": "all",
          "segm_detector": [
            "866",
            1
          ],
          "image": [
            "1064",
            0
          ]
        },
        "class_type": "SegmDetectorSEGS",
        "_meta": {
          "title": "SEGM Detector (SEGS)"
        }
      },
      "866": {
        "inputs": {
          "model_name": "segm/face_yolov8m-seg_60.pt"
        },
        "class_type": "UltralyticsDetectorProvider",
        "_meta": {
          "title": "UltralyticsDetectorProvider"
        }
      },
      "912": {
        "inputs": {
          "clip_name1": "t5xxl_fp16.safetensors",
          "clip_name2": "clip_l.safetensors",
          "type": "flux",
          "device": "default"
        },
        "class_type": "DualCLIPLoader",
        "_meta": {
          "title": "DualCLIPLoader"
        }
      },
      "914": {
        "inputs": {
          "unet_name": "flux1-dev.safetensors",
          "weight_dtype": "fp8_e4m3fn_fast"
        },
        "class_type": "UNETLoader",
        "_meta": {
          "title": "Load FLUX original Model"
        }
      },
      "922": {
        "inputs": {
          "PowerLoraLoaderHeaderWidget": {
            "type": "PowerLoraLoaderHeaderWidget"
          },
          "lora_1": {
            "on": true,
            "lora": "comfyui_portrait_lora64.safetensors",
            "strength": 1
          },
          "➕ Add Lora": "",
          "model": [
            "12",
            0
          ],
          "clip": [
            "11",
            0
          ]
        },
        "class_type": "Power Lora Loader (rgthree)",
        "_meta": {
          "title": "FLUX LoRA's Loader"
        }
      },
      "928": {
        "inputs": {
          "images": [
            "1039",
            0
          ]
        },
        "class_type": "PreviewImage",
        "_meta": {
          "title": "Preview Image"
        }
      },
      "929": {
        "inputs": {
          "threshold": 0.5000000000000001,
          "dilation": 0,
          "crop_factor": 1,
          "drop_size": 20,
          "labels": "all",
          "segm_detector": [
            "930",
            1
          ],
          "image": [
            "968",
            0
          ]
        },
        "class_type": "SegmDetectorSEGS",
        "_meta": {
          "title": "SEGM Detector (SEGS)"
        }
      },
      "930": {
        "inputs": {
          "model_name": "segm/PitEyeDetailer-v2-seg.pt"
        },
        "class_type": "UltralyticsDetectorProvider",
        "_meta": {
          "title": "UltralyticsDetectorProvider"
        }
      },
      "931": {
        "inputs": {
          "threshold": 0.5000000000000001,
          "dilation": 0,
          "crop_factor": 1,
          "drop_size": 20,
          "labels": "all",
          "segm_detector": [
            "932",
            1
          ],
          "image": [
            "968",
            0
          ]
        },
        "class_type": "SegmDetectorSEGS",
        "_meta": {
          "title": "SEGM Detector (SEGS)"
        }
      },
      "932": {
        "inputs": {
          "model_name": "segm/face_yolov8m-seg_60.pt"
        },
        "class_type": "UltralyticsDetectorProvider",
        "_meta": {
          "title": "UltralyticsDetectorProvider"
        }
      },
      "933": {
        "inputs": {
          "target": "x1",
          "order": true,
          "take_start": 0,
          "take_count": 1,
          "segs": [
            "931",
            0
          ]
        },
        "class_type": "ImpactSEGSOrderedFilter",
        "_meta": {
          "title": "SEGS Filter (ordered)"
        }
      },
      "934": {
        "inputs": {
          "base_segs": [
            "933",
            0
          ],
          "mask_segs": [
            "929",
            0
          ]
        },
        "class_type": "SubtractMaskForEach",
        "_meta": {
          "title": "Pixelwise(SEGS - SEGS)"
        }
      },
      "938": {
        "inputs": {
          "ckpt_name": "epicrealismXL_vxviLastfameRealism.safetensors"
        },
        "class_type": "CheckpointLoaderSimple",
        "_meta": {
          "title": "Load Checkpoint"
        }
      },
      "943": {
        "inputs": {
          "text": "detailed face, natural skin, detailed skin pore, realistic skin style, detailed style, 8k, ",
          "clip": [
            "953",
            1
          ]
        },
        "class_type": "CLIPTextEncode",
        "_meta": {
          "title": "CLIP Text Encode (Prompt)"
        }
      },
      "944": {
        "inputs": {
          "text": "(worst quality, low quality, illustration, 3d, 2d, painting, cartoons, sketch), make up, eye shadow, lipstick ",
          "clip": [
            "953",
            1
          ]
        },
        "class_type": "CLIPTextEncode",
        "_meta": {
          "title": "CLIP Text Encode (Prompt)"
        }
      },
      "947": {
        "inputs": {
          "guide_size": 512,
          "guide_size_for": true,
          "max_size": 720,
          "seed": 1039571901847053,
          "steps": 20,
          "cfg": 6,
          "sampler_name": "dpmpp_2m_sde",
          "scheduler": "simple",
          "denoise": 0.20000000000000004,
          "feather": 5,
          "noise_mask": true,
          "force_inpaint": true,
          "wildcard": "",
          "cycle": 1,
          "inpaint_model": false,
          "noise_mask_feather": 20,
          "tiled_encode": false,
          "tiled_decode": false,
          "image": [
            "968",
            0
          ],
          "segs": [
            "1005",
            0
          ],
          "model": [
            "953",
            0
          ],
          "clip": [
            "953",
            1
          ],
          "vae": [
            "938",
            2
          ],
          "positive": [
            "943",
            0
          ],
          "negative": [
            "944",
            0
          ]
        },
        "class_type": "DetailerForEach",
        "_meta": {
          "title": "Detailer (SEGS)"
        }
      },
      "953": {
        "inputs": {
          "PowerLoraLoaderHeaderWidget": {
            "type": "PowerLoraLoaderHeaderWidget"
          },
          "lora_1": {
            "on": true,
            "lora": "skin_texture_style_v3.safetensors",
            "strength": 0.75
          },
          "lora_2": {
            "on": true,
            "lora": "detailed.safetensors",
            "strength": 0.7
          },
          "➕ Add Lora": "",
          "model": [
            "938",
            0
          ],
          "clip": [
            "938",
            1
          ]
        },
        "class_type": "Power Lora Loader (rgthree)",
        "_meta": {
          "title": "Power Lora Loader (rgthree)"
        }
      },
      "958": {
        "inputs": {
          "segs": [
            "1005",
            0
          ]
        },
        "class_type": "SegsToCombinedMask",
        "_meta": {
          "title": "SEGS to MASK (combined)"
        }
      },
      "964": {
        "inputs": {
          "model_name": "1x-ITF-SkinDiffDetail-Lite-v1.pth"
        },
        "class_type": "UpscaleModelLoader",
        "_meta": {
          "title": "Load Upscale Model"
        }
      },
      "965": {
        "inputs": {
          "upscale_model": [
            "964",
            0
          ],
          "image": [
            "947",
            0
          ]
        },
        "class_type": "ImageUpscaleWithModel",
        "_meta": {
          "title": "Upscale Image (using Model)"
        }
      },
      "968": {
        "inputs": {
          "aggressive": false,
          "image": [
            "376",
            0
          ]
        },
        "class_type": "FreeMemoryImage",
        "_meta": {
          "title": "Free Memory (Image)"
        }
      },
      "975": {
        "inputs": {
          "direction": "right",
          "match_image_size": true,
          "image1": [
            "613",
            0
          ],
          "image2": [
            "981",
            0
          ]
        },
        "class_type": "ImageConcanate",
        "_meta": {
          "title": "Image Concatenate"
        }
      },
      "976": {
        "inputs": {
          "aggressive": false,
          "image": [
            "342",
            0
          ]
        },
        "class_type": "FreeMemoryImage",
        "_meta": {
          "title": "Free Memory (Image)"
        }
      },
      "977": {
        "inputs": {
          "purge_cache": true,
          "purge_models": true,
          "anything": [
            "976",
            0
          ]
        },
        "class_type": "LayerUtility: PurgeVRAM",
        "_meta": {
          "title": "LayerUtility: Purge VRAM"
        }
      },
      "981": {
        "inputs": {
          "method": "mkl",
          "strength": 1,
          "image_ref": [
            "613",
            0
          ],
          "image_target": [
            "1068",
            0
          ]
        },
        "class_type": "ColorMatch",
        "_meta": {
          "title": "Color Match"
        }
      },
      "999": {
        "inputs": {
          "low_threshold": 23,
          "high_threshold": 11,
          "resolution": [
            "1009",
            2
          ],
          "image": [
            "1000",
            0
          ]
        },
        "class_type": "CannyEdgePreprocessor",
        "_meta": {
          "title": "Canny Edge"
        }
      },
      "1000": {
        "inputs": {
          "image": [
            "376",
            0
          ],
          "mask": [
            "1001",
            0
          ]
        },
        "class_type": "ETN_ApplyMaskToImage",
        "_meta": {
          "title": "Apply Mask to Image"
        }
      },
      "1001": {
        "inputs": {
          "segs": [
            "934",
            0
          ]
        },
        "class_type": "SegsToCombinedMask",
        "_meta": {
          "title": "SEGS to MASK (combined)"
        }
      },
      "1002": {
        "inputs": {
          "channel": "red",
          "image": [
            "999",
            0
          ]
        },
        "class_type": "ImageToMask",
        "_meta": {
          "title": "Convert Image to Mask"
        }
      },
      "1005": {
        "inputs": {
          "combined": false,
          "crop_factor": 3,
          "bbox_fill": false,
          "drop_size": 10,
          "contour_fill": false,
          "mask": [
            "1011",
            0
          ]
        },
        "class_type": "MaskToSEGS",
        "_meta": {
          "title": "MASK to SEGS"
        }
      },
      "1009": {
        "inputs": {
          "value": [
            "376",
            0
          ]
        },
        "class_type": "ImpactImageInfo",
        "_meta": {
          "title": "ImpactImageInfo"
        }
      },
      "1011": {
        "inputs": {
          "mask1": [
            "1013",
            0
          ],
          "mask2": [
            "1002",
            0
          ]
        },
        "class_type": "SubtractMask",
        "_meta": {
          "title": "Pixelwise(MASK - MASK)"
        }
      },
      "1013": {
        "inputs": {
          "segs": [
            "934",
            0
          ]
        },
        "class_type": "SegsToCombinedMask",
        "_meta": {
          "title": "SEGS to MASK (combined)"
        }
      },
      "1015": {
        "inputs": {
          "rgthree_comparer": {
            "images": [
              {
                "name": "A",
                "selected": true,
                "url": "/api/view?filename=rgthree.compare._temp_iaudj_00003_.png&type=temp&subfolder=&rand=0.19605997356740534"
              },
              {
                "name": "B",
                "selected": true,
                "url": "/api/view?filename=rgthree.compare._temp_iaudj_00004_.png&type=temp&subfolder=&rand=0.19605997356740534"
              }
            ]
          },
          "image_a": [
            "981",
            0
          ],
          "image_b": [
            "376",
            0
          ]
        },
        "class_type": "Image Comparer (rgthree)",
        "_meta": {
          "title": "Image Comparer (rgthree)"
        }
      },
      "1025": {
        "inputs": {
          "output": "",
          "source": [
            "1027",
            0
          ]
        },
        "class_type": "Display Any (rgthree)",
        "_meta": {
          "title": "Display Any (rgthree)"
        }
      },
      "1026": {
        "inputs": {
          "output": "",
          "source": [
            "1055",
            2
          ]
        },
        "class_type": "Display Any (rgthree)",
        "_meta": {
          "title": "Display Any (rgthree)"
        }
      },
      "1027": {
        "inputs": {
          "frame_a": 1,
          "frame_b": 1,
          "frame_c": 1,
          "frame_d": 36,
          "frame_e": 48,
          "frame_f": 60,
          "frame_g": 72,
          "text_a": [
            "1069",
            0
          ],
          "text_b": [
            "1069",
            0
          ]
        },
        "class_type": "StringConcatenate",
        "_meta": {
          "title": "String Concatenate 📅��🅝"
        }
      },
      "1035": {
        "inputs": {
          "value": [
            "614",
            0
          ]
        },
        "class_type": "ImpactImageInfo",
        "_meta": {
          "title": "ImpactImageInfo"
        }
      },
      "1039": {
        "inputs": {
          "direction": "right",
          "match_image_size": true,
          "image1": [
            "975",
            0
          ],
          "image2": [
            "614",
            0
          ]
        },
        "class_type": "ImageConcanate",
        "_meta": {
          "title": "Image Concatenate"
        }
      },
      "1040": {
        "inputs": {
          "Number": "1"
        },
        "class_type": "Float",
        "_meta": {
          "title": "Denoise"
        }
      },
      "1041": {
        "inputs": {
          "Number": "5"
        },
        "class_type": "Float",
        "_meta": {
          "title": "Mask Blur"
        }
      },
      "1042": {
        "inputs": {
          "Number": "3"
        },
        "class_type": "Int",
        "_meta": {
          "title": "Int"
        }
      },
      "1043": {
        "inputs": {
          "Number": "128"
        },
        "class_type": "Int",
        "_meta": {
          "title": "Int"
        }
      },
      "1044": {
        "inputs": {
          "Number": "1536"
        },
        "class_type": "Int",
        "_meta": {
          "title": "Int"
        }
      },
      "1045": {
        "inputs": {
          "String": "this is a pair of images，the left side highlights [prompt], the right person has this face,"
        },
        "class_type": "String",
        "_meta": {
          "title": "String"
        }
      },
      "1046": {
        "inputs": {
          "clip_name": "sigclip_vision_patch14_384.safetensors"
        },
        "class_type": "CLIPVisionLoader",
        "_meta": {
          "title": "Load CLIP Vision"
        }
      },
      "1055": {
        "inputs": {
          "text_input": "",
          "task": "more_detailed_caption",
          "fill_mask": true,
          "keep_model_loaded": false,
          "max_new_tokens": 1024,
          "num_beams": 3,
          "do_sample": true,
          "output_mask_select": "",
          "seed": 327154228594459,
          "image": [
            "614",
            0
          ],
          "florence2_model": [
            "1058",
            0
          ]
        },
        "class_type": "Florence2Run",
        "_meta": {
          "title": "Florence2Run"
        }
      },
      "1058": {
        "inputs": {
          "model": "microsoft/Florence-2-base",
          "precision": "fp16",
          "attention": "sdpa",
          "convert_to_safetensors": false
        },
        "class_type": "DownloadAndLoadFlorence2Model",
        "_meta": {
          "title": "DownloadAndLoadFlorence2Model"
        }
      },
      "1062": {
        "inputs": {
          "image": "b0f348d5-ece9-4dca-b1f5-83c2e6d560a8.png"
        },
        "class_type": "LoadImage",
        "_meta": {
          "title": "Load Image"
        }
      },
      "1063": {
        "inputs": {
          "image": "xs10.png"
        },
        "class_type": "LoadImage",
        "_meta": {
          "title": "Load Image"
        }
      },
      "1064": {
        "inputs": {
          "image": "xs6 (1).png"
        },
        "class_type": "LoadImage",
        "_meta": {
          "title": "Load Image"
        }
      },
      "1065": {
        "inputs": {
          "images": [
            "1066",
            0
          ]
        },
        "class_type": "PreviewImage",
        "_meta": {
          "title": "Preview Image"
        }
      },
      "1066": {
        "inputs": {
          "prompt": "place this brunnete small size thin female model wearing this beige long dress paired with black sandals on this spanish stone villa with olive tree background. full height, 3/4 angle",
          "aspect_ratio": "9:16",
          "max_quality": false,
          "guidance_scale": 3.5,
          "num_images": 1,
          "safety_tolerance": "5",
          "output_format": "jpeg",
          "sync_mode": false,
          "seed": 3321636038,
          "image_1": [
            "1064",
            0
          ],
          "image_2": [
            "1062",
            0
          ],
          "image_3": [
            "1067",
            0
          ],
          "image_4": [
            "1063",
            0
          ]
        },
        "class_type": "FluxProKontextMulti_fal",
        "_meta": {
          "title": "Flux Pro Kontext Multi (fal)"
        }
      },
      "1067": {
        "inputs": {
          "image": "6dc55d53-3379-4b11-ad01-a2986cca12db.webp"
        },
        "class_type": "LoadImage",
        "_meta": {
          "title": "Load Image"
        }
      },
      "1068": {
        "inputs": {
          "method": "mkl",
          "strength": 1,
          "image_ref": [
            "613",
            0
          ],
          "image_target": [
            "965",
            0
          ]
        },
        "class_type": "ColorMatch",
        "_meta": {
          "title": "Color Match"
        }
      },
      "1069": {
        "inputs": {
          "old": "[prompt]",
          "new": [
            "1055",
            2
          ],
          "use_regex": false,
          "string": [
            "1045",
            0
          ]
        },
        "class_type": "String Replace (mtb)",
        "_meta": {
          "title": "String Replace (mtb)"
        }
      }
    }
  }
}
```

### Output Format
```json
{
  "id": "sync-uuid-string",
  "status": "COMPLETED",
  "output": {
    "images": [
      {
        "filename": "ComfyUI_00001_.png",
        "type": "base64",
        "data": "iVBORw0KGgoAAAANSUhEUg..."
      }
    ]
  },
  "delayTime": 123,
  "executionTime": 4567
}
```

---

## 🧪 Testing & Validation

### Working Test Request
We have successfully tested complex workflows including:
- Multiple input images
- Advanced FLUX models
- Custom nodes (PuLID Flux, Impact, etc.)
- Complex image processing pipelines
- Face detection and enhancement
- Style transfer and color matching

### Test Results
- **Image Generation**: Successful
- **Custom Models**: Working correctly
- **Custom Nodes**: Functioning properly
- **Complex Workflows**: Processing successfully
- **Performance**: Acceptable response times

---

## Deployment Process

### Step 1: Base Image Creation
1. Modified official Dockerfile to include WAS Node Suite
2. Built custom base image with automated installations

### Step 2: Custom Model Installation
1. Deployed base image to Hyperstack Cloud.
2. Used `docker exec` to install custom models
3. Created final image with all customizations

### Step 3: Production Deployment
1. Pushed final image to Docker Hub
2. Created RunPod serverless endpoint
3. Configured environment variables
4. Tested and validated functionality

---

## Cost & Resource Management

### Serverless Benefits
- **Pay-per-use**: Only charged for actual processing time
- **Auto-scaling**: Containers start/stop based on demand
- **Resource optimization**: GPU resources shared efficiently

### Resource Configuration
- **GPU Type**: Based on model requirements
- **Container Disk**: Sized for custom models
- **Memory**: Optimized for workflow complexity

---

## Security & Best Practices

### Security Measures
- **API Key Authentication**: Required for all requests
- **Container Isolation**: Each job runs in isolated environment
- **Image Validation**: Custom images scanned for security

### Environment Variables
```bash
# Core configuration
RUNPOD_HANDLER_MODULE=handler
RUNPOD_HANDLER_FUNCTION=handler

# RunPod configuration
REFRESH_WORKER=false
SERVE_API_LOCALLY=false

#for new release use testing true/false
testing=false

```

---

## Performance Metrics

### Current Performance
- **Response Time**: Acceptable for complex workflows
- **Success Rate**: High (based on testing)
- **Resource Utilization**: Optimized
- **Scalability**: Auto-scaling working correctly

### Monitoring
- **Job Status**: Tracked via RunPod dashboard
- **Error Handling**: Proper error reporting
- **Logging**: Comprehensive logging for debugging

---

## Maintenance & Updates

### Update Process
1. **Base Image Updates**: Modify Dockerfile and rebuild
2. **Custom Models**: Use `docker exec` for new models
3. **Custom Nodes**: Install via ComfyUI UI
4. **Endpoint Updates**: Redeploy with new image

### Backup Strategy
- **Image Registry**: Docker Hub backup
- **Configuration**: Version controlled
- **Workflows**: Stored as JSON files

---

---

## 📞 Support & Troubleshooting

### Common Issues
1. **Queue Problems**: Usually related to resource constraints
2. **Model Loading**: Check model file paths and names

### Debugging Steps
1. Check RunPod endpoint logs
2. Test with simpler workflows first
3. Monitor resource usage
4. Use same template without any env variables to run comfy as stateless so we can verify everything from UI using connect feature.

---

## Additional Resources

### Documentation
- [RunPod Documentation](https://docs.runpod.io/)
- [ComfyUI Documentation](https://github.com/comfyanonymous/ComfyUI)
- [Original worker-comfyui Repository](https://github.com/runpod-workers/worker-comfyui)

### Tools & Utilities
- **ComfyUI CLI**: For model and node management
- **Docker**: For container management
- **RunPod Dashboard**: For endpoint monitoring

---

## Deliverables Summary

| Component | Status | Details |
|-----------|--------|---------|
| Custom Docker Image | Complete | `docker.io/fashion2025/my-comfyui-models:v3` |
| WAS Node Suite | Automated | Installed via Dockerfile |
| Custom Nodes | Manual | Installed via ComfyUI UI |
| Custom Models | Manual | Installed via `docker exec` |
| Serverless Endpoint | Production | `https://api.runpod.ai/v2/s426ojl4svhd9e/run` |
| Testing | Validated | Complex workflow processing successfully |
| Documentation | Complete | This document and technical details |

---

**Project Status**:  **PRODUCTION READY**

This custom ComfyUI serverless endpoint is now ready for team use and can handle complex AI image generation workflows with our specific models and custom nodes. 