# Fashion Lab API Integration Guide

This comprehensive guide explains how the Fashion Lab API has been integrated into the FashionLab platform, including recent authentication fixes and improvements.

## 🚨 Critical Authentication Fix

**IMPORTANT**: This integration previously had authentication format issues that prevented image storage. All Fashion Lab API calls must use the correct JWT format:

- ❌ **INCORRECT**: `Authorization: Bearer ${token}`
- ✅ **CORRECT**: `Authorization: jwt ${token}`

## Overview

The Fashion Lab API provides AI-powered image generation capabilities. We've integrated both v1 (text-based) and v2 (image-based) endpoints with secure image storage and comprehensive error handling.

## Architecture

```
Frontend (Image Generator UI)
    ↓
FashionLabImageService
    ↓
Supabase Edge Functions
    ├── /functions/v1/generate-images (Image generation)
    ├── /functions/v1/queue-status (Status checking & storage)
    ├── /functions/v1/fashion-lab-proxy (General API proxy)
    └── /functions/v1/fashion-lab-image-proxy (Image access proxy)
    ↓
Fashion Lab API (https://fashionlab.notfirst.rodeo)
    ↓
Supabase Storage (ai-generated bucket)
    ↓
Database (ai_generated_images table)
```

## Complete Image Generation Workflow

### 1. Generation Request
```typescript
// User initiates generation in UI
const { generateImages } = useFashionLabImages();

await generateImages({
  prompt: "Fashion model wearing elegant dress",
  faceImage: base64Image,
  image2: garmentImage,
  image3: poseImage,
  image4: backgroundImage
});
```

### 2. Edge Function Processing
```typescript
// generate-images function
1. Validates user authentication
2. Converts base64 images to blobs
3. Creates multipart form data
4. Calls Fashion Lab API with JWT token
5. Returns queue_id for polling
```

### 3. Status Polling & Storage
```typescript
// queue-status function
1. Polls Fashion Lab API for completion
2. Downloads completed images with JWT auth
3. Uploads to Supabase storage bucket
4. Creates records in ai_generated_images table
5. Returns Supabase URLs to frontend
```

### 4. UI Display
```typescript
// Frontend displays stored images
const { data: generatedImages } = useGeneratedImages(collectionId);
// Images are served from Supabase storage with proper authentication
```

## Authentication Format Issues Fixed

### Components Affected
The following components had incorrect authentication format and have been fixed:

| Component | File | Issue | Status |
|-----------|------|-------|--------|
| Queue Status Function | `supabase/functions/queue-status/index.ts` | Used `Bearer ${token}` for image download | ✅ Fixed |
| Fashion Lab Proxy | `supabase/functions/fashion-lab-proxy/index.ts` | Used `Bearer ${token}` for API calls | ✅ Fixed |
| Image Proxy Function | `supabase/functions/fashion-lab-image-proxy/index.ts` | Used `Bearer ${token}` for image access | ✅ Fixed |
| API Test Component | `src/pages/demo/FashionLabAPITest.tsx` | Used `Bearer 2ms4LQBtkbvJ8RwFmBht` | ✅ Fixed |

### Root Cause
All Fashion Lab API integrations were using the standard `Bearer` token format instead of the Fashion Lab-specific `jwt` format, causing 401 authentication errors.

## Environment Variables

Add these to your `.env.local`:

```env
# Fashion Lab API Configuration
FASHION_LAB_API_URL=https://fashionlab.notfirst.rodeo
FASHIONLAB_JWT_SECRET=2ms4LQBtkbvJ8RwFmBht
```

Set in Supabase secrets:
```bash
supabase secrets set FASHIONLAB_JWT_SECRET=2ms4LQBtkbvJ8RwFmBht
```

## Enhanced Error Logging

We've added comprehensive error logging to help diagnose issues:

### Queue Status Function Logging
- Detailed Fashion Lab API response logging
- Image download progress tracking
- Storage upload success/failure logging
- Database insert error details
- Authentication failure detection

### Error Log Examples
```typescript
// Success logs
console.log('Processing image 1/3:', imageUrl);
console.log('Successfully downloaded image 1, size: 401484 bytes');
console.log('Successfully uploaded image 1 to storage:', storagePath);
console.log('Successfully created ai_generated_images record:', recordId);

// Error logs
console.error('Failed to download image 1: HTTP 401');
console.error('Image URL:', imageUrl);
console.error('Failed to upload image 1 to storage:', uploadError);
console.error('Failed to create ai_generated_images record:', dbError);
```

## Testing

### 1. Comprehensive End-to-End Test

Use the comprehensive test script to verify the complete workflow:

```bash
node test-complete-image-generation-flow.js
```

This script tests:
- ✅ User authentication
- ✅ Image generation request
- ✅ Queue status polling
- ✅ Image download with correct JWT format
- ✅ Storage upload to Supabase
- ✅ Database record creation
- ✅ Image accessibility via public URLs

### 2. Run Edge Functions Locally

```bash
# Start Supabase
supabase start

# Deploy functions with secrets
supabase functions deploy generate-images
supabase functions deploy queue-status
supabase functions deploy fashion-lab-proxy
supabase functions deploy fashion-lab-image-proxy
```

### 3. Test Individual Components

```bash
# Test image download authentication
node test-image-download.js

# Test queue status with real queue ID
node test-queue-status.js

# Check database records
node check-ai-images.js
```

## Frontend Integration

### Modern Hook-Based Integration

```typescript
import { useFashionLabImages } from '@/hooks/useFashionLabImages';

// In your component
const {
  generateImages,
  isGenerating,
  progress,
  error,
  generatedImages
} = useFashionLabImages({
  collectionId: 'your-collection-id',
  onComplete: (images) => console.log('Generation complete:', images)
});

// Generate images
await generateImages({
  prompt: "Fashion model wearing elegant dress",
  faceImage: base64FaceImage,
  image2: base64GarmentImage,
  image3: base64PoseImage,
  image4: base64BackgroundImage
});

// Display generated images
{generatedImages.map((image) => (
  <img
    key={image.id}
    src={image.image_url}
    alt="Generated fashion image"
  />
))}
```

### Service Layer Integration

```typescript
import { FashionLabImageService } from '@/services/fashionLabImageService';

// Direct service usage
const { queue_id } = await FashionLabImageService.generateImages({
  prompt: "Your prompt here",
  faceImage: base64Image,
  image2: garmentImage,
  image3: poseImage,
  image4: backgroundImage,
  collectionId: collectionId,
  storeOnCompletion: true
});

// Poll for completion with automatic storage
const result = await FashionLabImageService.waitForCompletion(
  queue_id,
  collectionId,
  true // storeImages
);
```

## Deployment

### 1. Deploy All Edge Functions

```bash
# Deploy all Fashion Lab related functions
supabase functions deploy generate-images
supabase functions deploy queue-status
supabase functions deploy fashion-lab-proxy
supabase functions deploy fashion-lab-image-proxy

# Set secrets (use the correct secret name)
supabase secrets set FASHIONLAB_JWT_SECRET=2ms4LQBtkbvJ8RwFmBht
```

### 2. Verify Deployment

```bash
# Check function status
supabase functions list

# Check secrets
supabase secrets list

# Test deployed functions
curl -X POST https://your-project.supabase.co/functions/v1/queue-status \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"queue_id": "test"}'
```

### 3. Production Environment

Ensure your production environment has:
- ✅ All edge functions deployed
- ✅ FASHIONLAB_JWT_SECRET set correctly
- ✅ Storage bucket `ai-generated` exists
- ✅ RLS policies are active
- ✅ Database migrations applied

## Troubleshooting

### 🚨 Authentication Format Issues

**Symptoms:**
- Generation completes successfully (shows "All photos uploaded successfully")
- No images appear in the generated images section
- Empty results from `ai_generated_images` table query
- HTTP 401 errors in function logs

**Root Cause:**
Using incorrect authentication format for Fashion Lab API calls.

**Solution:**
Ensure all Fashion Lab API calls use the correct JWT format:

```typescript
// ❌ WRONG - This will cause 401 errors
const response = await fetch(imageUrl, {
  headers: {
    'Authorization': `Bearer ${fashionLabToken}`,
  },
});

// ✅ CORRECT - This works
const response = await fetch(imageUrl, {
  headers: {
    'Authorization': `jwt ${fashionLabToken}`,
  },
});
```

### How to Verify the Fix

1. **Check Function Logs:**
   ```bash
   # Look for successful image downloads
   "Successfully downloaded image 1, size: 401484 bytes"
   "Successfully uploaded image 1 to storage"
   "Successfully created ai_generated_images record"
   ```

2. **Test Image Download:**
   ```bash
   node test-image-download.js
   # Should show: "Status with Bearer token: 200"
   ```

3. **Check Database:**
   ```bash
   node check-ai-images.js
   # Should show: "Total ai_generated_images records: > 0"
   ```

### Common Issues & Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **Authentication Format** | 401 errors, no images stored | Use `jwt ${token}` not `Bearer ${token}` |
| **Missing JWT Secret** | 500 errors in functions | Set `FASHIONLAB_JWT_SECRET` in Supabase |
| **Storage Permissions** | Images download but don't store | Check RLS policies on `ai-generated` bucket |
| **Database Permissions** | Storage works but no DB records | Check RLS policies on `ai_generated_images` table |
| **Queue Timeout** | Polling stops before completion | Increase `maxAttempts` in polling logic |
| **CORS Errors** | Frontend can't call functions | Ensure functions return proper CORS headers |

### Debug Mode

Enable comprehensive logging:
```typescript
// In edge functions
console.log('Fashion Lab API response:', JSON.stringify(result, null, 2));
console.log('Request parameters:', { queue_id, collection_id, store_images });
console.log('Image URLs to process:', imageUrls);
console.log('Processing image 1/3:', imageUrl);
console.log('Successfully downloaded image, size:', blob.size);
```

## Database Schema

### ai_generated_images Table
```sql
CREATE TABLE ai_generated_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  queue_id TEXT NOT NULL,
  collection_id UUID NOT NULL REFERENCES collections(id),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  user_id UUID NOT NULL REFERENCES users(id),
  image_url TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  prompt TEXT,
  selected BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Storage Bucket Structure
```
ai-generated/
  {organization_id}/
    {collection_id}/
      {timestamp}_{queue_id}_{index}.png
```

## Security Considerations

1. **JWT Token Security**: Store `FASHIONLAB_JWT_SECRET` securely in Supabase secrets
2. **Authentication**: All requests require valid Supabase session
3. **Authorization**: Users can only access their organization's images
4. **RLS Policies**: Automatic enforcement at database and storage level
5. **Input Validation**: Validate all inputs before sending to API
6. **Error Handling**: Don't expose internal errors to frontend
7. **Rate Limiting**: Implement rate limiting in edge functions

## Performance Optimizations

1. **Image Storage**: Images are downloaded once and stored in Supabase
2. **CDN Delivery**: Images served from Supabase CDN
3. **Efficient Polling**: Exponential backoff for queue status checks
4. **Batch Processing**: Multiple images processed in parallel
5. **Error Recovery**: Graceful fallback to original URLs on storage failure

## Monitoring & Analytics

### Key Metrics to Track
- Generation success rate
- Average generation time
- Storage success rate
- Error rates by type
- User adoption metrics

### Monitoring Tools
```typescript
// Track generation metrics
await supabase
  .from('generation_metrics')
  .insert({
    queue_id,
    status: 'completed',
    generation_time_ms: Date.now() - startTime,
    images_generated: imageUrls.length,
    images_stored: storedCount
  });
```

## Testing Instructions

### 1. Pre-Testing Checklist
- [ ] Supabase is running locally (`supabase start`)
- [ ] All edge functions are deployed
- [ ] `FASHIONLAB_JWT_SECRET` is set in Supabase secrets
- [ ] Test user exists in database
- [ ] Collection exists for testing

### 2. Run Comprehensive Test Suite

```bash
# Test complete end-to-end flow
node test-complete-image-generation-flow.js

# Expected output:
# ✅ Signed in successfully
# ✅ Using collection: Test Collection
# ✅ Generate response received
# ✅ Generation completed!
# ✅ Found 3 stored images for queue
# ✅ Image is accessible via public URL
# 🎉 Complete image generation flow test PASSED!
```

### 3. Individual Component Tests

```bash
# Test authentication format
node test-image-download.js
# Should show: "Status with Bearer token: 200"

# Check database state
node check-ai-images.js
# Should show: "Total ai_generated_images records: > 0"

# Test specific queue
node test-queue-status.js
# Should show: "Status: completed, Stored: true"
```

### 4. UI Testing

1. **Navigate to Image Generator**: `/collections/{id}/generate`
2. **Upload Images**: Face, garment, pose, background images
3. **Enter Prompt**: "Fashion model wearing elegant dress"
4. **Click Generate**: Should show progress indicator
5. **Wait for Completion**: Should show "All photos uploaded successfully"
6. **Check Results**: Images should appear in generated images section

### 5. Verify Fix is Working

```bash
# Check function logs for success indicators
supabase functions logs --function queue-status

# Look for these success messages:
# "Successfully downloaded image 1, size: 401484 bytes"
# "Successfully uploaded image 1 to storage"
# "Successfully created ai_generated_images record"
```

## Best Practices

### 1. Error Handling
```typescript
try {
  const result = await generateImages(params);
  // Handle success
} catch (error) {
  if (error.message.includes('401')) {
    // Authentication issue - check JWT format
  } else if (error.message.includes('storage')) {
    // Storage issue - check bucket permissions
  }
  // Handle other errors
}
```

### 2. Image Validation
```typescript
// Validate images before generation
const validateImage = (base64Image: string) => {
  if (!base64Image.startsWith('data:image/')) {
    throw new Error('Invalid image format');
  }

  const sizeInBytes = (base64Image.length * 3) / 4;
  if (sizeInBytes > 5 * 1024 * 1024) { // 5MB limit
    throw new Error('Image too large');
  }
};
```

### 3. Polling Best Practices
```typescript
// Use exponential backoff for polling
const pollWithBackoff = async (queueId: string) => {
  let attempt = 0;
  let delay = 1000; // Start with 1 second

  while (attempt < 20) {
    const status = await checkQueueStatus(queueId);

    if (status.status === 'completed') {
      return status;
    }

    await new Promise(resolve => setTimeout(resolve, delay));
    delay = Math.min(delay * 1.5, 10000); // Max 10 seconds
    attempt++;
  }

  throw new Error('Polling timeout');
};
```

## Future Enhancements

1. **Real-time Updates**: WebSocket-based progress updates
2. **Batch Generation**: Support multiple prompts in one request
3. **Image Optimization**: Automatic thumbnail generation
4. **AI Metadata**: Extract colors, tags, and descriptions
5. **Version Control**: Track image iterations and variations
6. **Workflow Integration**: Auto-approve/reject based on criteria
7. **Cost Optimization**: Intelligent caching and compression

---

## 📚 Related Documentation

- [Fashion Lab API Examples](./development/integrations/fashionlab-api-examples.md)
- [Integration Summary](./development/integrations/integration-summary.md)
- [Database Schema](./database-schema-final.md)
- [Troubleshooting Guide](./troubleshooting/supabase-common-issues.md)

## 🔗 Quick Links

- **Supabase Dashboard**: [Local](http://127.0.0.1:54323) | [Staging](https://supabase.com/dashboard/project/qnfmiotatmkoumlymynq)
- **Fashion Lab API**: https://fashionlab.notfirst.rodeo
- **Test Scripts**: `/test-*.js` files in project root
- **Edge Functions**: `/supabase/functions/`

---

**Last Updated**: January 2025
**Status**: ✅ Authentication issues resolved, fully functional