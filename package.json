{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "type-check": "tsc --noEmit", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:all": "npm run test", "migrate:assets": "ts-node --project tsconfig.node.json src/scripts/migrateAssets.ts", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:status": "supabase status", "supabase:logs": "supabase logs", "supabase:migration:new": "supabase migration new", "supabase:migration:list": "supabase migration list", "supabase:db:diff": "supabase db diff", "supabase:db:push": "supabase db push", "supabase:link:staging": "supabase link --project-ref qnfmiotatmkoumlymynq", "supabase:link:production": "supabase link --project-ref cpelxqvcjnbpnphttzsn", "migrate:staging": "node scripts/deployment/deploy-migrations.js staging", "migrate:production": "node scripts/deployment/deploy-migrations.js production", "validate:staging": "./scripts/validate-before-deploy.sh -e staging", "validate:production": "./scripts/validate-before-deploy.sh -e production", "validate": "./scripts/validate-before-deploy.sh", "setup:test-data": "./scripts/setup-comprehensive-test-data.sh local", "setup:test-data:images": "./scripts/setup-comprehensive-test-data.sh local --with-sample-images", "setup:test-data:clean": "./scripts/setup-comprehensive-test-data.sh local --cleanup-first --with-sample-images", "setup:staging": "./scripts/setup-comprehensive-test-data.sh staging --with-sample-images", "dev:check": "node scripts/setup/check-dev-environment.js", "env:check": "node scripts/validation/check-env-vars.js", "status": "node scripts/validation/environment-status.js", "setup": "npm run dev:check && npm run env:check"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "file-saver": "^2.0.5", "form-data": "^4.0.4", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "p-queue": "^8.1.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.30.0", "recharts": "^2.12.7", "resend": "^4.1.2", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/file-saver": "^2.0.7", "@types/jszip": "^3.4.0", "@types/node": "^22.14.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^26.0.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^3.0.9"}}