{"permissions": {"allow": ["Bash(gh pr:*)", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "Bash(git add:*)", "Bash(git commit:*)", "mcp__linear__linear_getIssues", "mcp__linear__linear_searchIssues", "mcp__linear__linear_createIssue", "mcp__linear__linear_getTeams", "mcp__linear__linear_getIssueById", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run typecheck:*)", "Bash(npm run type-check:*)", "Bash(supabase migration:*)", "Bash(supabase db reset:*)", "Bash(supabase status:*)", "Bash(supabase db:*)", "Bash(psql:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres -f /Users/<USER>/Documents/fashionlab-v1/supabase/seed.sql)", "Bash(npm run setup:test-data:*)", "Bash(npm run dev:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(npm run supabase:mcp:help:*)", "Bash(supabase link:*)", "mcp__postgres__query", "Bash(supabase projects:*)", "<PERSON><PERSON>(env)", "Bash(grep:*)", "Bash(git reset:*)", "Bash(git checkout:*)", "Bash(git push:*)", "<PERSON><PERSON>(echo:*)", "mcp__supabase-mcp-server__get_logs", "Bash(supabase functions list:*)", "mcp__supabase-mcp-server__list_projects", "mcp__supabase-mcp-server__execute_sql", "Bash(git log:*)", "Bash(npm run supabase:reset:*)", "Bash(npm run supabase:db:query:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "mcp__linear__linear_createComment", "Bash(npx supabase db push:*)", "Bash(npx supabase link:*)", "Bash(git cherry-pick:*)", "Bash(supabase:*)", "<PERSON><PERSON>(cat:*)", "Bash(cp:*)", "Bash(npm install:*)", "Bash(PGPASSWORD:*)"], "deny": []}}