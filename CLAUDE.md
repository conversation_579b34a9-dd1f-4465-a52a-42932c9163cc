# CLAUDE.md

Fashion Lab - AI-powered fashion imagery platform for brands.

# Coding rules

@docs/RULES.md

## Quick Start

```bash
npm install && npm run dev           # Start local dev
npm run supabase:start               # Start local database
npm run test                         # Run tests
```

## Project IDs

- **Staging**: `qnfmiotatmkoumlymynq`
- **Production**: `cpelxqvcjnbpnphttzsn`

## Essential Imports

@docs/GETTING-STARTED.md             # Setup and common issues
@docs/REFERENCE.md                   # Tech stack, database schema, deployment
@docs/COMMANDS.md                    # Available Claude commands

## Key Workflows

### Deploy to Staging

```bash
git push origin main                 # Frontend auto-deploys via Vercel
```

### Deploy to Production

```bash
gh pr create --base production --head main --title "Deploy: [description]"
# After merge, production auto-deploys
```

### Database Migrations

Use Supabase MCP tools:

- `mcp__supabase__list_migrations --project_id [id]`
- `mcp__supabase__apply_migration --project_id [id] --name [name] --query [sql]`

### Database Access (IPv6 Workaround)

```bash
# Use pooler URLs to avoid IPv6 issues:
# Staging
psql "postgresql://postgres.qnfmiotatmkoumlymynq:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"

# Production  
psql "postgresql://postgres.cpelxqvcjnbpnphttzsn:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

## Development Practices

- **Commits**: Small, logical commits with clear messages
- **Branches**: Feature branches for significant work
- **Linear**: Update issue status when deploying
- **Testing**: Run `npm run lint` and `npm run typecheck` before committing

## MCP Servers

- **Linear**: `mcp__linear__*` - Issue tracking
- **Supabase**: `mcp__supabase__*` - Database operations
- **IDE**: `mcp__ide__*` - Code diagnostics
